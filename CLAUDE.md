# 变频模块控制系统开发记录

## 项目概述
基于新通信协议重写的变频模块控制上位机系统，支持CAN扩展帧通信和BCD编码。

## 主要文件
- `converter_main.py` - 主程序入口
- `converter_control_window_v2.py` - 平铺布局GUI界面
- `converter_module_protocol.py` - 协议实现和BCD编解码
- `converter_can_interface.py` - CAN扩展帧接口
- `doc/通信协议.md` - 通信协议规范文档

## 技术特性
- CAN扩展帧通信 (29位ID)
- 1Mbps波特率
- BCD编码参数传输
- 1s自动查询间隔
- 支持标准和校准变频模块
- 实时状态监控

## UI特点
- 平铺布局，所有控件可见
- Material Design配色方案
- 14px字体，优化控件高度
- 频率步进10kHz，衰减步进1dB

## 最新修复 (2025-01-09)

### 1. 线程安全问题修复
- 修复QTextCursor跨线程操作导致的闪退
- 使用QMetaObject.invokeMethod确保UI更新在主线程执行
- 添加窗口关闭时的信号断开和资源清理

### 2. 频率BCD编码修复
- 修正频率编码格式完全符合协议要求
- 2230.60MHz → [0x00, 0x22, 0x30, 0x60, 0x00, 0x00]
- 第一字节固定为0x00，后续字节按BCD格式存储
- 同步更新解码函数，正确解析协议数据

## 测试命令
```bash
python converter_main.py
python test_bcd.py  # 测试BCD编码
```

## 开发完成状态
✅ 协议实现完整
✅ GUI界面优化完成
✅ BCD编解码修复
✅ 线程安全问题解决
✅ 自动查询稳定运行
✅ 所有用户需求已实现