# 变频模块控制系统 V1.0

## 概述
本程序是基于CAN总线的变频模块控制上位机软件，根据`doc/通信协议.md`中定义的协议实现了对变频模块的完整控制功能。

## 系统要求
- Windows 7/10/11
- Python 3.6+
- USBCAN-II设备及驱动
- PyQt5

## 文件说明

### 新增文件（变频模块控制）
- `converter_main.py` - 主程序入口
- `converter_control_window.py` - 主窗口界面
- `converter_module_protocol.py` - 变频模块通信协议实现
- `converter_can_interface.py` - 支持扩展帧的CAN接口

### 原有文件（保留）
- `main_window_v3.py` - 原射频模块控制程序
- `rf_module_protocol.py` - 原射频模块协议
- `usbcan2_interface.py` - 原CAN接口
- 其他支持文件

## 功能特点

### 1. 完整协议实现
- 支持CAN扩展帧（29位ID）
- 1Mbps波特率通信
- BCD编码的参数传输
- 多帧数据组装

### 2. 控制功能
- **频率控制**
  - 射频频率设置（普通变频模块）
  - 接收频率设置（校零变频模块）
  - 发射频率设置（校零变频模块专用）
  
- **参数控制**
  - 衰减控制（0-62dB，0.1dB步进）
  - 本振输出选择（LO1/LO2）
  - 电源控制（上电/下电/部分下电）

### 3. 状态监控
- 模块工作状态
- 本振锁定状态
- 频率、衰减实时显示
- 输入/输出功率监测
- 电压、电流、温度监测
- 参考源状态

### 4. 自动查询
- 50ms定时查询（符合协议要求）
- 4帧响应数据自动组装
- 实时状态更新

## 使用方法

### 1. 启动程序
```bash
python converter_main.py
```

### 2. 连接设备
1. 选择CAN通道（0或1）
2. 选择波特率（默认1000kbps）
3. 设置模块地址（1-16）
4. 如果是校零变频模块，勾选"校零变频模块"
5. 点击"连接"按钮

### 3. 参数设置
#### 普通变频模块
1. 在"参数控制"标签页设置：
   - 射频频率（MHz）
   - 衰减（dB）
   - 本振输出（LO1/LO2）
   - 电源控制
2. 点击相应的"设置"按钮发送命令

#### 校零变频模块
1. 勾选"校零变频模块"选项
2. 除上述参数外，还可设置：
   - 接收频率（作为射频频率）
   - 发射频率（ERF）

### 4. 状态监控
1. 点击"查询一次"手动查询
2. 勾选"自动查询"启动定时查询
3. 在"状态监控"标签页查看实时状态

### 5. 日志查看
在"通信日志"标签页可查看所有CAN通信记录

## 通信协议说明

### 扩展帧ID格式
```
| MAB (8bit) | DateNum (8bit) | SUMD (8bit) | CMD (8bit) |
```
- MAB: 模块地址（1-16）
- DateNum: 数据段顺序号
- SUMD: 总数据段数
- CMD: 命令码

### 命令码定义
- 0x01: 参数控制1（RF/RRF频率）
- 0x02: 参数控制2（衰减、本振、电源）
- 0x03: 参数控制3（ERF发射频率）
- 0x06: 查询命令

### 数据格式
- 频率：6字节BCD编码（MHz）
- 衰减：2字节BCD编码（0.1dB单位）
- 功率：3字节（符号+2字节BCD）
- 温度：2字节（符号+1字节BCD）

## 注意事项

1. **设备连接**
   - 确保USBCAN-II设备驱动已正确安装
   - CAN总线需要正确终端电阻匹配

2. **模块地址**
   - 模块地址由硬件地址线决定
   - 范围：1-16

3. **校零变频模块**
   - 勾选"校零变频模块"后显示额外的发射频率控制
   - 接收频率对应协议中的RRF
   - 发射频率对应协议中的ERF

4. **查询响应**
   - 查询响应为4帧数据
   - 帧间隔约400us
   - 总数据32字节

## 故障排除

1. **无法连接设备**
   - 检查USBCAN-II设备是否连接
   - 检查驱动是否安装
   - 尝试更换USB端口

2. **无响应**
   - 检查CAN总线连接
   - 确认模块地址正确
   - 检查波特率设置（应为1Mbps）

3. **数据异常**
   - 检查CAN总线终端电阻
   - 降低波特率测试
   - 查看通信日志排查问题

## 版本历史
- V1.0 (2024-09) - 初始版本，实现完整的变频模块控制协议