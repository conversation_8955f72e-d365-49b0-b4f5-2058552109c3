"""
变频模块专用CAN接口
支持扩展帧的CAN通信接口，基于USBCAN-II设备
"""

import time
from ctypes import c_int
from zlgcan import ZCAN, ZCAN_CHANNEL_INIT_CONFIG, ZCAN_Transmit_Data


class ConverterCANInterface:
    """变频模块CAN接口类"""
    
    def __init__(self):
        self.zcan = ZCAN()
        self.device_handle = None
        self.channel_handle = None
        self.device_type = 3  # ZCAN_USBCAN1 = 3 (兼容USBCAN-II设备)
        self.device_index = 0
        self.log_callback = None
        
    def set_log_callback(self, callback):
        """设置日志回调函数"""
        self.log_callback = callback
        
    def _log(self, message):
        """输出日志"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(f"[CAN] {message}")
            
    def open_device(self, channel=0, baudrate=1000):
        """
        打开CAN设备并初始化通道
        
        Args:
            channel: CAN通道号（0或1）
            baudrate: 波特率（kbps），默认1000kbps
            
        Returns:
            bool: 成功返回True
        """
        try:
            # 打开设备
            self.device_handle = self.zcan.OpenDevice(self.device_type, self.device_index, 0)
            if self.device_handle == 0:
                self._log("无法打开USBCAN-II设备")
                return False
                
            # 配置通道
            cfg = ZCAN_CHANNEL_INIT_CONFIG()
            cfg.can_type = 0  # TYPE_CAN
            cfg.config.can.filter = 0  # 不使用过滤
            cfg.config.can.mode = 0  # 正常模式
            cfg.config.can.acc_code = 0
            cfg.config.can.acc_mask = 0xffffffff
            
            # 设置波特率
            # 1000kbps: timing0=0x00, timing1=0x14
            # 500kbps:  timing0=0x00, timing1=0x1C
            # 250kbps:  timing0=0x01, timing1=0x1C
            if baudrate == 1000:
                cfg.config.can.timing0 = 0x00
                cfg.config.can.timing1 = 0x14
            elif baudrate == 500:
                cfg.config.can.timing0 = 0x00
                cfg.config.can.timing1 = 0x1C
            elif baudrate == 250:
                cfg.config.can.timing0 = 0x01
                cfg.config.can.timing1 = 0x1C
            else:
                self._log(f"不支持的波特率: {baudrate}kbps，使用默认1000kbps")
                cfg.config.can.timing0 = 0x00
                cfg.config.can.timing1 = 0x14
                
            # 初始化通道
            self.channel_handle = self.zcan.InitCAN(self.device_handle, channel, cfg)
            if self.channel_handle == 0:
                self._log(f"无法初始化CAN通道 {channel}")
                return False
                
            # 启动通道
            ret = self.zcan.StartCAN(self.channel_handle)
            if ret != 1:
                self._log("无法启动CAN通道")
                return False
                
            self._log(f"CAN设备已打开: 通道={channel}, 波特率={baudrate}kbps")
            return True
            
        except Exception as e:
            self._log(f"打开设备错误: {e}")
            return False
            
    def close_device(self):
        """关闭CAN设备"""
        if self.channel_handle:
            self.zcan.ResetCAN(self.channel_handle)
            self.channel_handle = None
            
        if self.device_handle:
            self.zcan.CloseDevice(self.device_handle)
            self.device_handle = None
            
        self._log("CAN设备已关闭")
        
    def send_extended_frame(self, ext_id, data):
        """
        发送扩展帧
        
        Args:
            ext_id: 29位扩展帧ID
            data: 数据字节列表（最多8字节）
            
        Returns:
            bool: 发送成功返回True
        """
        if not self.channel_handle:
            self._log("通道未初始化")
            return False
            
        if len(data) > 8:
            self._log("数据长度超过8字节")
            return False
            
        try:
            # 创建发送数据结构
            transmit_data = (ZCAN_Transmit_Data * 1)()
            
            # 填充结构
            transmit_data[0].transmit_type = 0  # 正常发送
            transmit_data[0].frame.can_id = ext_id & 0x1FFFFFFF  # 29位ID
            transmit_data[0].frame.can_dlc = len(data)
            transmit_data[0].frame.eff = 1  # 扩展帧（29位ID）
            transmit_data[0].frame.rtr = 0  # 数据帧
            transmit_data[0].frame.err = 0
            transmit_data[0].frame.pad = 0
            
            # 复制数据字节
            for i, byte in enumerate(data):
                transmit_data[0].frame.data[i] = byte
                
            # 发送帧
            ret = self.zcan.Transmit(self.channel_handle, transmit_data, 1)
            if ret != 1:
                self._log(f"发送CAN消息失败，返回码: {ret}")
                return False
                
            # 记录发送的数据
            hex_str = ' '.join(f'{b:02X}' for b in data)
            self._log(f"TX [EXT ID: 0x{ext_id:08X}]: {hex_str}")
            
            return True
            
        except Exception as e:
            self._log(f"发送扩展帧错误: {e}")
            return False
            
    def receive_frames(self, timeout=1000, max_frames=10):
        """
        接收CAN帧
        
        Args:
            timeout: 超时时间（毫秒）
            max_frames: 最大接收帧数
            
        Returns:
            list: 接收到的帧列表，每个元素为字典{'ext_id': id, 'data': [bytes]}
        """
        if not self.channel_handle:
            self._log("通道未初始化")
            return []
            
        try:
            msgs, count = self.zcan.Receive(self.channel_handle, max_frames, c_int(timeout))
            
            if count == 0:
                return []
                
            frames = []
            for i in range(count):
                frame = msgs[i]
                data = []
                for j in range(frame.frame.can_dlc):
                    data.append(frame.frame.data[j])
                    
                # 提取扩展帧ID
                ext_id = frame.frame.can_id
                
                frames.append({
                    'ext_id': ext_id,
                    'data': data,
                    'is_extended': frame.frame.eff == 1
                })
                
                # 记录接收的数据
                hex_str = ' '.join(f'{b:02X}' for b in data)
                if frame.frame.eff == 1:
                    self._log(f"RX [EXT ID: 0x{ext_id:08X}]: {hex_str}")
                else:
                    self._log(f"RX [STD ID: 0x{ext_id:03X}]: {hex_str}")
                    
            return frames
            
        except Exception as e:
            self._log(f"接收CAN消息错误: {e}")
            return []
            
    def receive_multi_frame_response(self, expected_frames=4, timeout=500):
        """
        接收多帧响应（用于查询响应）
        
        Args:
            expected_frames: 期望的帧数（默认4帧）
            timeout: 总超时时间（毫秒）
            
        Returns:
            list: 接收到的帧列表，如果未收到完整响应返回None
        """
        if not self.channel_handle:
            self._log("通道未初始化")
            return None
            
        try:
            start_time = time.time()
            received_frames = []
            
            while len(received_frames) < expected_frames:
                # 计算剩余超时时间
                elapsed = (time.time() - start_time) * 1000
                remaining_timeout = max(10, timeout - elapsed)
                
                if remaining_timeout <= 0:
                    self._log(f"接收超时，已接收 {len(received_frames)}/{expected_frames} 帧")
                    return None
                    
                # 接收帧
                frames = self.receive_frames(int(remaining_timeout), expected_frames - len(received_frames))
                
                if frames:
                    received_frames.extend(frames)
                else:
                    # 没有收到数据，检查是否超时
                    if (time.time() - start_time) * 1000 > timeout:
                        self._log(f"接收超时，已接收 {len(received_frames)}/{expected_frames} 帧")
                        return None
                        
            # 验证帧顺序（通过扩展帧ID中的DateNum字段）
            sorted_frames = []
            for i in range(1, expected_frames + 1):
                found = False
                for frame in received_frames:
                    # 从扩展帧ID中提取DateNum（第16-23位）
                    date_num = (frame['ext_id'] >> 16) & 0xFF
                    if date_num == i:
                        sorted_frames.append(frame)
                        found = True
                        break
                        
                if not found:
                    self._log(f"缺少第 {i} 帧")
                    return None
                    
            self._log(f"成功接收 {expected_frames} 帧响应")
            return sorted_frames
            
        except Exception as e:
            self._log(f"接收多帧响应错误: {e}")
            return None
            
    def clear_receive_buffer(self):
        """清空接收缓冲区"""
        if not self.channel_handle:
            return
            
        # 读取并丢弃最多100条消息
        self.zcan.Receive(self.channel_handle, 100, c_int(10))