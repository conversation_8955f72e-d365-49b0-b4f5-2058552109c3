"""
变频模块控制主窗口
提供变频模块的参数设置和状态监控功能
"""

import sys
import time
import threading
from datetime import datetime
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QPushButton, QTextEdit, QGroupBox, QGridLayout,
                             QLabel, QComboBox, QMessageBox, QApplication,
                             QDoubleSpinBox, QSpinBox, QCheckBox, QTabWidget,
                             QRadioButton, QButtonGroup)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QTextCursor, QPalette, QColor

from converter_can_interface import ConverterCANInterface
from converter_module_protocol import (ConverterModuleProtocol, 
                                      LOOutput, PowerControl, 
                                      ModuleStatus, ReferenceSource)


class QueryThread(QThread):
    """查询线程，定时发送查询命令并接收响应"""
    
    status_received = pyqtSignal(dict)  # 状态数据信号
    log_message = pyqtSignal(str)       # 日志消息信号
    
    def __init__(self, protocol, module_addr):
        super().__init__()
        self.protocol = protocol
        self.module_addr = module_addr
        self.running = False
        self.query_interval = 50  # 50ms查询间隔
        
    def run(self):
        """线程主循环"""
        self.running = True
        while self.running:
            try:
                # 发送查询命令
                if self.protocol.send_query(self.module_addr):
                    # 等待并接收4帧响应
                    time.sleep(0.01)  # 短暂延时
                    frames = self.protocol.can.receive_multi_frame_response(4, 500)
                    
                    if frames:
                        # 解析响应数据
                        status = self.protocol.parse_query_response(frames)
                        if status:
                            self.status_received.emit(status)
                            
                # 等待下次查询
                time.sleep(self.query_interval / 1000.0)
                
            except Exception as e:
                self.log_message.emit(f"查询线程错误: {e}")
                time.sleep(1)
                
    def stop(self):
        """停止线程"""
        self.running = False
        self.wait()


class ConverterControlWindow(QMainWindow):
    """变频模块控制主窗口"""
    
    def __init__(self):
        super().__init__()
        self.can = ConverterCANInterface()
        self.protocol = ConverterModuleProtocol(self.can)
        self.query_thread = None
        self.is_calibration_module = False  # 是否为校零变频模块
        
        # 设置协议回调
        self.protocol.set_log_callback(self.append_log)
        self.protocol.set_status_callback(self.update_status_text)
        self.can.set_log_callback(self.append_log)
        
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("变频模块控制系统 V1.0")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 顶部连接控制区
        main_layout.addWidget(self.create_connection_group())
        
        # 中部标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.addTab(self.create_control_tab(), "参数控制")
        self.tab_widget.addTab(self.create_status_tab(), "状态监控")
        self.tab_widget.addTab(self.create_log_tab(), "通信日志")
        main_layout.addWidget(self.tab_widget)
        
        # 底部状态栏
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("未连接")
        
        # 设置样式
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                min-height: 30px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QLabel {
                min-height: 25px;
            }
        """)
        
    def create_connection_group(self):
        """创建连接控制组"""
        group = QGroupBox("连接设置")
        layout = QHBoxLayout()
        
        # CAN通道选择
        layout.addWidget(QLabel("CAN通道:"))
        self.channel_combo = QComboBox()
        self.channel_combo.addItems(["通道0", "通道1"])
        layout.addWidget(self.channel_combo)
        
        # 波特率选择
        layout.addWidget(QLabel("波特率:"))
        self.baudrate_combo = QComboBox()
        self.baudrate_combo.addItems(["1000 kbps", "500 kbps", "250 kbps"])
        layout.addWidget(self.baudrate_combo)
        
        # 模块地址
        layout.addWidget(QLabel("模块地址:"))
        self.addr_spin = QSpinBox()
        self.addr_spin.setRange(1, 16)
        self.addr_spin.setValue(4)
        layout.addWidget(self.addr_spin)
        
        # 模块类型
        self.calibration_check = QCheckBox("校零变频模块")
        self.calibration_check.stateChanged.connect(self.on_module_type_changed)
        layout.addWidget(self.calibration_check)
        
        layout.addStretch()
        
        # 连接按钮
        self.connect_btn = QPushButton("连接")
        self.connect_btn.clicked.connect(self.toggle_connection)
        self.connect_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        layout.addWidget(self.connect_btn)
        
        group.setLayout(layout)
        return group
        
    def create_control_tab(self):
        """创建控制标签页"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # 频率控制组
        freq_group = QGroupBox("频率控制")
        freq_layout = QGridLayout()
        
        # 射频/接收频率
        freq_layout.addWidget(QLabel("射频频率 (MHz):"), 0, 0)
        self.rf_freq_spin = QDoubleSpinBox()
        self.rf_freq_spin.setRange(0, 20000)
        self.rf_freq_spin.setDecimals(6)
        self.rf_freq_spin.setSingleStep(0.1)
        self.rf_freq_spin.setValue(2230.567800)
        freq_layout.addWidget(self.rf_freq_spin, 0, 1)
        
        self.set_rf_btn = QPushButton("设置射频频率")
        self.set_rf_btn.clicked.connect(self.set_rf_frequency)
        freq_layout.addWidget(self.set_rf_btn, 0, 2)
        
        # 发射频率（仅校零变频模块）
        self.erf_label = QLabel("发射频率 (MHz):")
        freq_layout.addWidget(self.erf_label, 1, 0)
        self.erf_freq_spin = QDoubleSpinBox()
        self.erf_freq_spin.setRange(0, 20000)
        self.erf_freq_spin.setDecimals(6)
        self.erf_freq_spin.setSingleStep(0.1)
        self.erf_freq_spin.setValue(2301.880000)
        freq_layout.addWidget(self.erf_freq_spin, 1, 1)
        
        self.set_erf_btn = QPushButton("设置发射频率")
        self.set_erf_btn.clicked.connect(self.set_erf_frequency)
        freq_layout.addWidget(self.set_erf_btn, 1, 2)
        
        # 初始隐藏发射频率控制
        self.erf_label.setVisible(False)
        self.erf_freq_spin.setVisible(False)
        self.set_erf_btn.setVisible(False)
        
        freq_group.setLayout(freq_layout)
        layout.addWidget(freq_group)
        
        # 参数控制组
        param_group = QGroupBox("参数控制")
        param_layout = QGridLayout()
        
        # 衰减
        param_layout.addWidget(QLabel("衰减 (dB):"), 0, 0)
        self.att_spin = QDoubleSpinBox()
        self.att_spin.setRange(0, 62)
        self.att_spin.setDecimals(1)
        self.att_spin.setSingleStep(0.1)
        self.att_spin.setValue(2.1)
        param_layout.addWidget(self.att_spin, 0, 1)
        
        # 本振输出
        param_layout.addWidget(QLabel("本振输出:"), 1, 0)
        self.lo_combo = QComboBox()
        self.lo_combo.addItems(["LO1", "LO2"])
        param_layout.addWidget(self.lo_combo, 1, 1)
        
        # 电源控制
        param_layout.addWidget(QLabel("电源控制:"), 2, 0)
        self.power_combo = QComboBox()
        self.power_combo.addItems(["下电", "上电", "部分下电"])
        self.power_combo.setCurrentIndex(1)
        param_layout.addWidget(self.power_combo, 2, 1)
        
        # 设置按钮
        self.set_params_btn = QPushButton("设置参数")
        self.set_params_btn.clicked.connect(self.set_parameters)
        self.set_params_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        param_layout.addWidget(self.set_params_btn, 3, 0, 1, 2)
        
        param_group.setLayout(param_layout)
        layout.addWidget(param_group)
        
        # 快捷操作组
        quick_group = QGroupBox("快捷操作")
        quick_layout = QHBoxLayout()
        
        # 一键设置所有参数
        self.set_all_btn = QPushButton("设置所有参数")
        self.set_all_btn.clicked.connect(self.set_all_parameters)
        self.set_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                font-weight: bold;
                min-height: 40px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        quick_layout.addWidget(self.set_all_btn)
        
        # 查询状态
        self.query_once_btn = QPushButton("查询一次")
        self.query_once_btn.clicked.connect(self.query_once)
        quick_layout.addWidget(self.query_once_btn)
        
        # 自动查询
        self.auto_query_check = QCheckBox("自动查询")
        self.auto_query_check.stateChanged.connect(self.toggle_auto_query)
        quick_layout.addWidget(self.auto_query_check)
        
        quick_group.setLayout(quick_layout)
        layout.addWidget(quick_group)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget
        
    def create_status_tab(self):
        """创建状态监控标签页"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # 状态显示组
        status_group = QGroupBox("模块状态")
        status_layout = QGridLayout()
        
        # 创建状态标签
        self.status_labels = {}
        status_items = [
            ("模块状态:", "module_status"),
            ("本振1锁定:", "lo1_lock"),
            ("本振2锁定:", "lo2_lock"),
            ("本振输出:", "lo_output"),
            ("频率 (MHz):", "frequency"),
            ("衰减 (dB):", "attenuation"),
            ("输入功率 (dBm):", "input_power"),
            ("输出功率 (dBm):", "output_power"),
            ("电压 (V):", "voltage"),
            ("电流 (mA):", "current"),
            ("温度 (℃):", "temperature"),
            ("电源状态:", "power_status"),
            ("参考源:", "reference"),
            ("发射频率 (MHz):", "tx_frequency")
        ]
        
        for i, (label_text, key) in enumerate(status_items):
            row = i % 7
            col = (i // 7) * 2
            
            label = QLabel(label_text)
            status_layout.addWidget(label, row, col)
            
            value_label = QLabel("--")
            value_label.setStyleSheet("QLabel { font-weight: bold; color: #333; }")
            status_layout.addWidget(value_label, row, col + 1)
            
            self.status_labels[key] = value_label
            
            # 校零变频模块才显示发射频率
            if key == "tx_frequency":
                label.setVisible(False)
                value_label.setVisible(False)
                
        status_group.setLayout(status_layout)
        layout.addWidget(status_group)
        
        # 状态文本显示
        self.status_text = QTextEdit()
        self.status_text.setReadOnly(True)
        self.status_text.setMaximumHeight(200)
        layout.addWidget(self.status_text)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget
        
    def create_log_tab(self):
        """创建日志标签页"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # 日志文本框
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.log_text)
        
        # 日志控制按钮
        btn_layout = QHBoxLayout()
        
        clear_btn = QPushButton("清空日志")
        clear_btn.clicked.connect(self.log_text.clear)
        btn_layout.addWidget(clear_btn)
        
        self.auto_scroll_check = QCheckBox("自动滚动")
        self.auto_scroll_check.setChecked(True)
        btn_layout.addWidget(self.auto_scroll_check)
        
        btn_layout.addStretch()
        layout.addLayout(btn_layout)
        
        widget.setLayout(layout)
        return widget
        
    def on_module_type_changed(self, state):
        """模块类型改变事件"""
        self.is_calibration_module = state == Qt.Checked
        
        # 显示/隐藏发射频率控制
        self.erf_label.setVisible(self.is_calibration_module)
        self.erf_freq_spin.setVisible(self.is_calibration_module)
        self.set_erf_btn.setVisible(self.is_calibration_module)
        
        # 显示/隐藏发射频率状态
        if "tx_frequency" in self.status_labels:
            tx_freq_label = self.status_labels["tx_frequency"]
            tx_freq_label.setVisible(self.is_calibration_module)
            # 找到对应的标签
            for i in range(self.tab_widget.widget(1).layout().itemAt(0).widget().layout().count()):
                item = self.tab_widget.widget(1).layout().itemAt(0).widget().layout().itemAt(i)
                if item and item.widget() and item.widget().text() == "发射频率 (MHz):":
                    item.widget().setVisible(self.is_calibration_module)
                    break
                    
    def toggle_connection(self):
        """切换连接状态"""
        if self.connect_btn.text() == "连接":
            self.connect_device()
        else:
            self.disconnect_device()
            
    def connect_device(self):
        """连接设备"""
        channel = self.channel_combo.currentIndex()
        baudrate_text = self.baudrate_combo.currentText()
        baudrate = int(baudrate_text.split()[0])
        
        if self.can.open_device(channel, baudrate):
            self.connect_btn.setText("断开")
            self.connect_btn.setStyleSheet("""
                QPushButton {
                    background-color: #f44336;
                    color: white;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #da190b;
                }
            """)
            
            self.status_bar.showMessage(f"已连接 - 通道{channel}, {baudrate}kbps")
            self.append_log("设备连接成功")
            
            # 启用控件
            self.enable_controls(True)
        else:
            QMessageBox.warning(self, "连接失败", "无法打开CAN设备")
            
    def disconnect_device(self):
        """断开设备"""
        # 停止自动查询
        if self.query_thread:
            self.query_thread.stop()
            self.query_thread = None
            self.auto_query_check.setChecked(False)
            
        self.can.close_device()
        
        self.connect_btn.setText("连接")
        self.connect_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        
        self.status_bar.showMessage("未连接")
        self.append_log("设备已断开")
        
        # 禁用控件
        self.enable_controls(False)
        
    def enable_controls(self, enabled):
        """启用/禁用控件"""
        self.set_rf_btn.setEnabled(enabled)
        self.set_erf_btn.setEnabled(enabled)
        self.set_params_btn.setEnabled(enabled)
        self.set_all_btn.setEnabled(enabled)
        self.query_once_btn.setEnabled(enabled)
        self.auto_query_check.setEnabled(enabled)
        
    def set_rf_frequency(self):
        """设置射频频率"""
        module_addr = self.addr_spin.value()
        freq = self.rf_freq_spin.value()
        
        if self.protocol.send_control_rf(module_addr, freq, self.is_calibration_module):
            self.append_log(f"射频频率设置成功: {freq} MHz")
        else:
            self.append_log("射频频率设置失败")
            
    def set_erf_frequency(self):
        """设置发射频率（校零变频模块）"""
        if not self.is_calibration_module:
            return
            
        module_addr = self.addr_spin.value()
        freq = self.erf_freq_spin.value()
        
        if self.protocol.send_control_erf(module_addr, freq):
            self.append_log(f"发射频率设置成功: {freq} MHz")
        else:
            self.append_log("发射频率设置失败")
            
    def set_parameters(self):
        """设置参数"""
        module_addr = self.addr_spin.value()
        att = self.att_spin.value()
        
        # 本振输出
        lo_output = LOOutput.LO1 if self.lo_combo.currentIndex() == 0 else LOOutput.LO2
        
        # 电源控制
        power_index = self.power_combo.currentIndex()
        if power_index == 0:
            power_control = PowerControl.POWER_OFF
        elif power_index == 1:
            power_control = PowerControl.POWER_ON
        else:
            power_control = PowerControl.PARTIAL_OFF
            
        if self.protocol.send_control_att(module_addr, att, lo_output, power_control):
            self.append_log(f"参数设置成功: 衰减={att}dB, 本振={lo_output.name}, 电源={power_control.name}")
        else:
            self.append_log("参数设置失败")
            
    def set_all_parameters(self):
        """设置所有参数"""
        # 设置射频频率
        self.set_rf_frequency()
        time.sleep(0.01)
        
        # 设置参数
        self.set_parameters()
        time.sleep(0.01)
        
        # 如果是校零变频模块，设置发射频率
        if self.is_calibration_module:
            self.set_erf_frequency()
            
        self.append_log("所有参数设置完成")
        
    def query_once(self):
        """查询一次"""
        module_addr = self.addr_spin.value()
        
        if self.protocol.send_query(module_addr):
            # 等待响应
            time.sleep(0.05)
            frames = self.can.receive_multi_frame_response(4, 500)
            
            if frames:
                status = self.protocol.parse_query_response(frames)
                if status:
                    self.update_status_display(status)
                    self.append_log("查询成功")
                else:
                    self.append_log("查询响应解析失败")
            else:
                self.append_log("未收到查询响应")
        else:
            self.append_log("发送查询命令失败")
            
    def toggle_auto_query(self, state):
        """切换自动查询"""
        if state == Qt.Checked:
            # 启动查询线程
            module_addr = self.addr_spin.value()
            self.query_thread = QueryThread(self.protocol, module_addr)
            self.query_thread.status_received.connect(self.update_status_display)
            self.query_thread.log_message.connect(self.append_log)
            self.query_thread.start()
            
            self.append_log("启动自动查询")
        else:
            # 停止查询线程
            if self.query_thread:
                self.query_thread.stop()
                self.query_thread = None
                
            self.append_log("停止自动查询")
            
    def update_status_display(self, status):
        """更新状态显示"""
        # 更新状态标签
        self.status_labels["module_status"].setText(status["module_status"].name)
        self.status_labels["lo1_lock"].setText(status["lo1_lock_status"].name)
        self.status_labels["lo2_lock"].setText(status["lo2_lock_status"].name)
        self.status_labels["lo_output"].setText(status["lo_output"].name)
        self.status_labels["frequency"].setText(f"{status['rf_frequency_mhz']:.6f}")
        self.status_labels["attenuation"].setText(f"{status['attenuation_db']:.1f}")
        self.status_labels["input_power"].setText(f"{status['input_power_dbm']:.2f}")
        self.status_labels["output_power"].setText(f"{status['output_power_dbm']:.2f}")
        self.status_labels["voltage"].setText(f"{status['voltage_v']:.2f}")
        self.status_labels["current"].setText(f"{status['current_ma']}")
        self.status_labels["temperature"].setText(f"{status['temperature_c']:.1f}")
        self.status_labels["power_status"].setText(status["power_control"].name)
        self.status_labels["reference"].setText(status["reference_source"].name)
        
        if self.is_calibration_module:
            self.status_labels["tx_frequency"].setText(f"{status['tx_frequency_mhz']:.6f}")
            
        # 根据状态设置颜色
        if status["module_status"] == ModuleStatus.NORMAL:
            self.status_labels["module_status"].setStyleSheet("QLabel { color: green; }")
        else:
            self.status_labels["module_status"].setStyleSheet("QLabel { color: red; }")
            
        if status["lo1_lock_status"] == ModuleStatus.NORMAL:
            self.status_labels["lo1_lock"].setStyleSheet("QLabel { color: green; }")
        else:
            self.status_labels["lo1_lock"].setStyleSheet("QLabel { color: red; }")
            
        if status["lo2_lock_status"] == ModuleStatus.NORMAL:
            self.status_labels["lo2_lock"].setStyleSheet("QLabel { color: green; }")
        else:
            self.status_labels["lo2_lock"].setStyleSheet("QLabel { color: red; }")
            
    def update_status_text(self, message):
        """更新状态文本"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.status_text.append(f"[{timestamp}] {message}")
        
        # 限制行数
        if self.status_text.document().blockCount() > 100:
            cursor = self.status_text.textCursor()
            cursor.movePosition(QTextCursor.Start)
            cursor.select(QTextCursor.BlockUnderCursor)
            cursor.removeSelectedText()
            
    def append_log(self, message):
        """添加日志"""
        self.log_text.append(message)
        
        # 自动滚动
        if self.auto_scroll_check.isChecked():
            self.log_text.moveCursor(QTextCursor.End)
            
        # 限制日志行数
        if self.log_text.document().blockCount() > 1000:
            cursor = self.log_text.textCursor()
            cursor.movePosition(QTextCursor.Start)
            cursor.select(QTextCursor.BlockUnderCursor)
            cursor.removeSelectedText()
            
    def closeEvent(self, event):
        """窗口关闭事件"""
        # 停止查询线程
        if self.query_thread:
            self.query_thread.stop()
            
        # 关闭CAN设备
        self.can.close_device()
        
        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle("Fusion")
    
    # 创建并显示主窗口
    window = ConverterControlWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()