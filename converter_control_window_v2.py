"""
变频模块控制主窗口 V2 - 平铺布局版本
所有控制和状态显示在同一界面，无需切换标签页
"""

import sys
import time
import threading
from datetime import datetime
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QPushButton, QTextEdit, QGroupBox, QGridLayout,
                             QLabel, QComboBox, QMessageBox, QApplication,
                             QDoubleSpinBox, QSpinBox, QCheckBox, QSplitter)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, pyqtSlot, QTimer, QMetaObject, Q_ARG
from PyQt5.QtGui import QFont, QTextCursor, QPalette, QColor

from converter_can_interface import ConverterCANInterface
from converter_module_protocol import (ConverterModuleProtocol, 
                                      LOOutput, PowerControl, 
                                      ModuleStatus, ReferenceSource)


class QueryThread(QThread):
    """查询线程，定时发送查询命令并接收响应"""
    
    status_received = pyqtSignal(dict)  # 状态数据信号
    log_message = pyqtSignal(str)       # 日志消息信号
    
    def __init__(self, protocol, module_addr):
        super().__init__()
        self.protocol = protocol
        self.module_addr = module_addr
        self.running = False
        self.query_interval = 1000  # 1s查询间隔
        
    def run(self):
        """线程主循环"""
        self.running = True
        while self.running:
            try:
                # 发送查询命令
                if self.protocol.send_query(self.module_addr):
                    # 等待并接收4帧响应
                    time.sleep(0.01)  # 短暂延时
                    frames = self.protocol.can.receive_multi_frame_response(4, 500)
                    
                    if frames:
                        # 解析响应数据
                        status = self.protocol.parse_query_response(frames)
                        if status:
                            self.status_received.emit(status)
                            
                # 等待下次查询
                time.sleep(self.query_interval / 1000.0)
                
            except Exception as e:
                self.log_message.emit(f"查询线程错误: {e}")
                time.sleep(1)
                
    def stop(self):
        """停止线程"""
        self.running = False
        # 等待线程结束，但设置超时避免无限等待
        if not self.wait(2000):  # 2秒超时
            self.terminate()  # 强制终止
            self.wait()  # 等待终止完成


class ConverterControlWindow(QMainWindow):
    """变频模块控制主窗口 - 平铺布局"""
    
    def __init__(self):
        super().__init__()
        self.can = ConverterCANInterface()
        self.protocol = ConverterModuleProtocol(self.can)
        self.query_thread = None
        self.is_calibration_module = False  # 是否为校零变频模块
        
        # 设置协议回调
        self.protocol.set_log_callback(self.append_log)
        self.protocol.set_status_callback(self.update_status_text)
        self.can.set_log_callback(self.append_log)
        
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面 - 平铺布局"""
        self.setWindowTitle("变频模块控制系统 V2.0 - 平铺布局")
        self.setGeometry(50, 50, 1600, 900)
        
        # 创建中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主垂直布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(5)
        
        # 顶部连接控制区
        main_layout.addWidget(self.create_connection_group())
        
        # 创建主分割器（水平分割）
        main_splitter = QSplitter(Qt.Horizontal)
        
        # 左侧区域 - 控制面板
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setSpacing(5)
        
        # 频率控制
        left_layout.addWidget(self.create_frequency_control())
        # 参数控制
        left_layout.addWidget(self.create_parameter_control())
        # 快捷操作
        left_layout.addWidget(self.create_quick_actions())
        left_layout.addStretch()
        
        # 中间区域 - 状态显示
        middle_panel = QWidget()
        middle_layout = QVBoxLayout(middle_panel)
        middle_layout.setSpacing(5)
        middle_layout.addWidget(self.create_status_display())
        
        # 右侧区域 - 日志
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setSpacing(5)
        right_layout.addWidget(self.create_log_panel())
        
        # 添加到分割器
        main_splitter.addWidget(left_panel)
        main_splitter.addWidget(middle_panel)
        main_splitter.addWidget(right_panel)
        
        # 设置分割器比例
        main_splitter.setSizes([450, 550, 600])
        
        main_layout.addWidget(main_splitter)
        
        # 底部状态栏
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("未连接")
        
        # 设置样式 - 更大字体和优化的配色
        self.setStyleSheet("""
            QMainWindow {
                background-color: #e8eaed;
            }
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 1px solid #d2d4d8;
                border-radius: 6px;
                margin-top: 14px;
                padding-top: 14px;
                background-color: transparent;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px 0 8px;
                color: #1a73e8;
                background-color: #e8eaed;
                font-size: 15px;
            }
            QPushButton {
                min-height: 36px;
                min-width: 90px;
                border: 1px solid #dadce0;
                border-radius: 4px;
                background-color: #ffffff;
                color: #3c4043;
                font-size: 14px;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: #f8f9fa;
                border-color: #5f6368;
            }
            QPushButton:pressed {
                background-color: #e8eaed;
            }
            QPushButton:disabled {
                background-color: #f1f3f4;
                color: #9aa0a6;
            }
            QLabel {
                min-height: 26px;
                color: #3c4043;
                font-size: 14px;
            }
            QTextEdit {
                border: 1px solid #dadce0;
                border-radius: 4px;
                background-color: #fafafa;
                font-size: 13px;
                color: #202124;
                padding: 6px;
            }
            QDoubleSpinBox, QSpinBox, QComboBox {
                min-height: 28px;
                max-height: 28px;
                border: 1px solid #dadce0;
                border-radius: 4px;
                padding: 4px 6px;
                background-color: #fafafa;
                color: #3c4043;
                font-size: 14px;
            }
            QDoubleSpinBox:hover, QSpinBox:hover, QComboBox:hover {
                border-color: #5f6368;
                background-color: #ffffff;
            }
            QDoubleSpinBox:focus, QSpinBox:focus, QComboBox:focus {
                border-color: #1a73e8;
                background-color: #ffffff;
                outline: none;
            }
            QCheckBox {
                font-size: 14px;
                color: #3c4043;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        
    def create_connection_group(self):
        """创建连接控制组"""
        group = QGroupBox("连接设置")
        group.setMaximumHeight(85)
        layout = QHBoxLayout()
        
        # CAN通道选择
        layout.addWidget(QLabel("CAN通道:"))
        self.channel_combo = QComboBox()
        self.channel_combo.addItems(["通道0", "通道1"])
        self.channel_combo.setMaximumWidth(100)
        layout.addWidget(self.channel_combo)
        
        # 波特率选择
        layout.addWidget(QLabel("波特率:"))
        self.baudrate_combo = QComboBox()
        self.baudrate_combo.addItems(["1000 kbps", "500 kbps", "250 kbps"])
        self.baudrate_combo.setMaximumWidth(120)
        layout.addWidget(self.baudrate_combo)
        
        # 模块地址
        layout.addWidget(QLabel("模块地址:"))
        self.addr_spin = QSpinBox()
        self.addr_spin.setRange(1, 16)
        self.addr_spin.setValue(4)
        self.addr_spin.setMaximumWidth(80)
        layout.addWidget(self.addr_spin)
        
        # 模块类型
        self.calibration_check = QCheckBox("校零变频模块")
        self.calibration_check.stateChanged.connect(self.on_module_type_changed)
        layout.addWidget(self.calibration_check)
        
        layout.addStretch()
        
        # 自动查询
        self.auto_query_check = QCheckBox("自动查询")
        self.auto_query_check.stateChanged.connect(self.toggle_auto_query)
        layout.addWidget(self.auto_query_check)
        
        # 连接按钮
        self.connect_btn = QPushButton("连接")
        self.connect_btn.clicked.connect(self.toggle_connection)
        self.connect_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                font-weight: bold;
                min-width: 100px;
                border: none;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
        """)
        layout.addWidget(self.connect_btn)
        
        group.setLayout(layout)
        return group
        
    def create_frequency_control(self):
        """创建频率控制组"""
        group = QGroupBox("频率控制")
        layout = QGridLayout()
        layout.setSpacing(8)
        
        # 射频/接收频率
        layout.addWidget(QLabel("射频频率(MHz):"), 0, 0)
        self.rf_freq_spin = QDoubleSpinBox()
        self.rf_freq_spin.setRange(0, 20000)
        self.rf_freq_spin.setDecimals(2)  # 保留2位小数，对应10kHz精度
        self.rf_freq_spin.setSingleStep(0.01)  # 10kHz = 0.01MHz
        self.rf_freq_spin.setValue(2230.56)
        layout.addWidget(self.rf_freq_spin, 0, 1)
        
        self.set_rf_btn = QPushButton("设置")
        self.set_rf_btn.clicked.connect(self.set_rf_frequency)
        self.set_rf_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
            }
            QPushButton:hover {
                background-color: #0069d9;
            }
            QPushButton:pressed {
                background-color: #0056b3;
            }
        """)
        layout.addWidget(self.set_rf_btn, 0, 2)
        
        # 发射频率（仅校零变频模块）
        self.erf_label = QLabel("发射频率(MHz):")
        layout.addWidget(self.erf_label, 1, 0)
        self.erf_freq_spin = QDoubleSpinBox()
        self.erf_freq_spin.setRange(0, 20000)
        self.erf_freq_spin.setDecimals(2)  # 保留2位小数，对应10kHz精度
        self.erf_freq_spin.setSingleStep(0.01)  # 10kHz = 0.01MHz
        self.erf_freq_spin.setValue(2301.88)
        layout.addWidget(self.erf_freq_spin, 1, 1)
        
        self.set_erf_btn = QPushButton("设置")
        self.set_erf_btn.clicked.connect(self.set_erf_frequency)
        self.set_erf_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
            }
            QPushButton:hover {
                background-color: #0069d9;
            }
            QPushButton:pressed {
                background-color: #0056b3;
            }
        """)
        layout.addWidget(self.set_erf_btn, 1, 2)
        
        # 初始隐藏发射频率控制
        self.erf_label.setVisible(False)
        self.erf_freq_spin.setVisible(False)
        self.set_erf_btn.setVisible(False)
        
        group.setLayout(layout)
        return group
        
    def create_parameter_control(self):
        """创建参数控制组"""
        group = QGroupBox("参数控制")
        layout = QGridLayout()
        layout.setSpacing(8)
        
        # 衰减
        layout.addWidget(QLabel("衰减(dB):"), 0, 0)
        self.att_spin = QSpinBox()  # 改为整数SpinBox
        self.att_spin.setRange(0, 62)
        self.att_spin.setSingleStep(1)  # 1dB步进
        self.att_spin.setValue(2)
        layout.addWidget(self.att_spin, 0, 1)
        
        # 本振输出
        layout.addWidget(QLabel("本振输出:"), 1, 0)
        self.lo_combo = QComboBox()
        self.lo_combo.addItems(["LO1", "LO2"])
        layout.addWidget(self.lo_combo, 1, 1)
        
        # 电源控制
        layout.addWidget(QLabel("电源控制:"), 2, 0)
        self.power_combo = QComboBox()
        self.power_combo.addItems(["下电", "上电", "部分下电"])
        self.power_combo.setCurrentIndex(1)
        layout.addWidget(self.power_combo, 2, 1)
        
        # 设置按钮
        self.set_params_btn = QPushButton("设置参数")
        self.set_params_btn.clicked.connect(self.set_parameters)
        self.set_params_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                font-weight: bold;
                border: none;
            }
            QPushButton:hover {
                background-color: #0069d9;
            }
            QPushButton:pressed {
                background-color: #0056b3;
            }
        """)
        layout.addWidget(self.set_params_btn, 3, 0, 1, 2)
        
        group.setLayout(layout)
        return group
        
    def create_quick_actions(self):
        """创建快捷操作组"""
        group = QGroupBox("快捷操作")
        layout = QVBoxLayout()
        layout.setSpacing(5)
        
        # 一键设置所有参数
        self.set_all_btn = QPushButton("一键设置所有参数")
        self.set_all_btn.clicked.connect(self.set_all_parameters)
        self.set_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                font-weight: bold;
                min-height: 35px;
                border: none;
            }
            QPushButton:hover {
                background-color: #138496;
            }
            QPushButton:pressed {
                background-color: #117a8b;
            }
        """)
        layout.addWidget(self.set_all_btn)
        
        # 查询状态
        self.query_once_btn = QPushButton("单次查询")
        self.query_once_btn.clicked.connect(self.query_once)
        self.query_once_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                min-height: 30px;
                border: none;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
            QPushButton:pressed {
                background-color: #545b62;
            }
        """)
        layout.addWidget(self.query_once_btn)
        
        group.setLayout(layout)
        return group
        
    def create_status_display(self):
        """创建状态显示组"""
        group = QGroupBox("实时状态监控")
        layout = QVBoxLayout()
        
        # 状态网格布局
        status_grid = QGridLayout()
        status_grid.setSpacing(5)
        
        # 创建状态标签
        self.status_labels = {}
        status_items = [
            ("模块状态:", "module_status", 0, 0),
            ("本振1锁定:", "lo1_lock", 1, 0),
            ("本振2锁定:", "lo2_lock", 2, 0),
            ("本振输出:", "lo_output", 3, 0),
            ("参考源:", "reference", 4, 0),
            ("电源状态:", "power_status", 5, 0),
            
            ("频率(MHz):", "frequency", 0, 2),
            ("衰减(dB):", "attenuation", 1, 2),
            ("输入功率(dBm):", "input_power", 2, 2),
            ("输出功率(dBm):", "output_power", 3, 2),
            ("电压(V):", "voltage", 4, 2),
            ("电流(mA):", "current", 5, 2),
            
            ("温度(℃):", "temperature", 6, 0),
            ("发射频率(MHz):", "tx_frequency", 6, 2)
        ]
        
        for label_text, key, row, col in status_items:
            # 标签
            label = QLabel(label_text)
            label.setStyleSheet("QLabel { font-weight: 500; font-size: 14px; color: #5f6368; }")
            status_grid.addWidget(label, row, col)
            
            # 值标签
            value_label = QLabel("--")
            value_label.setStyleSheet("QLabel { font-weight: bold; font-size: 14px; color: #1a73e8; background-color: #e8f0fe; padding: 5px; border: 1px solid #dadce0; border-radius: 4px; }")
            value_label.setMinimumWidth(120)
            status_grid.addWidget(value_label, row, col + 1)
            
            self.status_labels[key] = value_label
            
            # 校零变频模块才显示发射频率
            if key == "tx_frequency":
                label.setVisible(False)
                value_label.setVisible(False)
                self.tx_freq_label_text = label  # 保存引用
                
        layout.addLayout(status_grid)
        
        # 状态消息文本框
        self.status_text = QTextEdit()
        self.status_text.setReadOnly(True)
        self.status_text.setMaximumHeight(150)
        self.status_text.setFont(QFont("Consolas", 11))
        layout.addWidget(QLabel("状态消息:"))
        layout.addWidget(self.status_text)
        
        group.setLayout(layout)
        return group
        
    def create_log_panel(self):
        """创建日志面板"""
        group = QGroupBox("通信日志")
        layout = QVBoxLayout()
        
        # 日志文本框
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 11))
        layout.addWidget(self.log_text)
        
        # 日志控制按钮
        btn_layout = QHBoxLayout()
        
        clear_btn = QPushButton("清空日志")
        clear_btn.clicked.connect(self.log_text.clear)
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:pressed {
                background-color: #bd2130;
            }
        """)
        btn_layout.addWidget(clear_btn)
        
        self.auto_scroll_check = QCheckBox("自动滚动")
        self.auto_scroll_check.setChecked(True)
        btn_layout.addWidget(self.auto_scroll_check)
        
        btn_layout.addStretch()
        layout.addLayout(btn_layout)
        
        group.setLayout(layout)
        return group
        
    def on_module_type_changed(self, state):
        """模块类型改变事件"""
        self.is_calibration_module = state == Qt.Checked
        
        # 显示/隐藏发射频率控制
        self.erf_label.setVisible(self.is_calibration_module)
        self.erf_freq_spin.setVisible(self.is_calibration_module)
        self.set_erf_btn.setVisible(self.is_calibration_module)
        
        # 显示/隐藏发射频率状态
        if "tx_frequency" in self.status_labels:
            self.status_labels["tx_frequency"].setVisible(self.is_calibration_module)
            if hasattr(self, 'tx_freq_label_text'):
                self.tx_freq_label_text.setVisible(self.is_calibration_module)
                    
    def toggle_connection(self):
        """切换连接状态"""
        if self.connect_btn.text() == "连接":
            self.connect_device()
        else:
            self.disconnect_device()
            
    def connect_device(self):
        """连接设备"""
        channel = self.channel_combo.currentIndex()
        baudrate_text = self.baudrate_combo.currentText()
        baudrate = int(baudrate_text.split()[0])
        
        if self.can.open_device(channel, baudrate):
            self.connect_btn.setText("断开")
            self.connect_btn.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    font-weight: bold;
                    min-width: 100px;
                    border: none;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
                QPushButton:pressed {
                    background-color: #bd2130;
                }
            """)
            
            self.status_bar.showMessage(f"已连接 - 通道{channel}, {baudrate}kbps")
            self.append_log("✓ 设备连接成功")
            
            # 启用控件
            self.enable_controls(True)
        else:
            QMessageBox.warning(self, "连接失败", "无法打开CAN设备，请检查设备连接")
            
    def disconnect_device(self):
        """断开设备"""
        # 停止自动查询
        if self.query_thread:
            self.query_thread.stop()
            self.query_thread = None
            self.auto_query_check.setChecked(False)
            
        self.can.close_device()
        
        self.connect_btn.setText("连接")
        self.connect_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                font-weight: bold;
                min-width: 100px;
                border: none;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
        """)
        
        self.status_bar.showMessage("未连接")
        self.append_log("✗ 设备已断开")
        
        # 禁用控件
        self.enable_controls(False)
        
        # 清空状态显示
        for label in self.status_labels.values():
            label.setText("--")
            label.setStyleSheet("QLabel { font-weight: bold; font-size: 14px; color: #1a73e8; background-color: #e8f0fe; padding: 5px; border: 1px solid #dadce0; border-radius: 4px; }")
        
    def enable_controls(self, enabled):
        """启用/禁用控件"""
        self.set_rf_btn.setEnabled(enabled)
        self.set_erf_btn.setEnabled(enabled)
        self.set_params_btn.setEnabled(enabled)
        self.set_all_btn.setEnabled(enabled)
        self.query_once_btn.setEnabled(enabled)
        self.auto_query_check.setEnabled(enabled)
        
    def set_rf_frequency(self):
        """设置射频频率"""
        module_addr = self.addr_spin.value()
        freq = self.rf_freq_spin.value()
        
        if self.protocol.send_control_rf(module_addr, freq, self.is_calibration_module):
            self.append_log(f"✓ 射频频率设置成功: {freq:.2f} MHz")
            self.update_status_text(f"射频频率已设置为 {freq:.2f} MHz")
        else:
            self.append_log("✗ 射频频率设置失败")
            
    def set_erf_frequency(self):
        """设置发射频率（校零变频模块）"""
        if not self.is_calibration_module:
            return
            
        module_addr = self.addr_spin.value()
        freq = self.erf_freq_spin.value()
        
        if self.protocol.send_control_erf(module_addr, freq):
            self.append_log(f"✓ 发射频率设置成功: {freq:.2f} MHz")
            self.update_status_text(f"发射频率已设置为 {freq:.2f} MHz")
        else:
            self.append_log("✗ 发射频率设置失败")
            
    def set_parameters(self):
        """设置参数"""
        module_addr = self.addr_spin.value()
        att = self.att_spin.value()
        
        # 本振输出
        lo_output = LOOutput.LO1 if self.lo_combo.currentIndex() == 0 else LOOutput.LO2
        
        # 电源控制
        power_index = self.power_combo.currentIndex()
        if power_index == 0:
            power_control = PowerControl.POWER_OFF
        elif power_index == 1:
            power_control = PowerControl.POWER_ON
        else:
            power_control = PowerControl.PARTIAL_OFF
            
        if self.protocol.send_control_att(module_addr, att, lo_output, power_control):
            self.append_log(f"✓ 参数设置成功: 衰减={att}dB, 本振={lo_output.name}, 电源={power_control.name}")
            self.update_status_text(f"参数已更新: 衰减={att}dB, 本振={lo_output.name}, 电源={power_control.name}")
        else:
            self.append_log("✗ 参数设置失败")
            
    def set_all_parameters(self):
        """设置所有参数"""
        self.append_log(">>> 开始设置所有参数...")
        
        # 设置射频频率
        self.set_rf_frequency()
        time.sleep(0.01)
        
        # 设置参数
        self.set_parameters()
        time.sleep(0.01)
        
        # 如果是校零变频模块，设置发射频率
        if self.is_calibration_module:
            self.set_erf_frequency()
            
        self.append_log(">>> 所有参数设置完成")
        self.update_status_text("所有参数设置完成")
        
    def query_once(self):
        """查询一次"""
        module_addr = self.addr_spin.value()
        
        if self.protocol.send_query(module_addr):
            # 等待响应
            time.sleep(0.05)
            frames = self.can.receive_multi_frame_response(4, 500)
            
            if frames:
                status = self.protocol.parse_query_response(frames)
                if status:
                    self.update_status_display(status)
                    self.append_log("✓ 查询成功")
                    self.update_status_text("状态查询成功")
                else:
                    self.append_log("✗ 查询响应解析失败")
            else:
                self.append_log("✗ 未收到查询响应")
        else:
            self.append_log("✗ 发送查询命令失败")
            
    def toggle_auto_query(self, state):
        """切换自动查询"""
        if state == Qt.Checked:
            # 启动查询线程
            module_addr = self.addr_spin.value()
            self.query_thread = QueryThread(self.protocol, module_addr)
            self.query_thread.status_received.connect(self.update_status_display)
            self.query_thread.log_message.connect(self.append_log)
            self.query_thread.start()
            
            self.append_log("▶ 启动自动查询 (1s间隔)")
            self.update_status_text("自动查询已启动")
        else:
            # 停止查询线程
            if self.query_thread:
                self.query_thread.stop()
                self.query_thread = None
                
            self.append_log("■ 停止自动查询")
            self.update_status_text("自动查询已停止")
            
    def update_status_display(self, status):
        """更新状态显示"""
        try:
            # 检查窗口是否仍然有效
            if not self.isVisible():
                return
                
            # 更新状态标签
            self.status_labels["module_status"].setText(status["module_status"].name)
            self.status_labels["lo1_lock"].setText(status["lo1_lock_status"].name)
            self.status_labels["lo2_lock"].setText(status["lo2_lock_status"].name)
            self.status_labels["lo_output"].setText(status["lo_output"].name)
            self.status_labels["frequency"].setText(f"{status['rf_frequency_mhz']:.2f}")
            self.status_labels["attenuation"].setText(f"{int(status['attenuation_db'])}")
            self.status_labels["input_power"].setText(f"{status['input_power_dbm']:.2f}")
            self.status_labels["output_power"].setText(f"{status['output_power_dbm']:.2f}")
            self.status_labels["voltage"].setText(f"{status['voltage_v']:.2f}")
            self.status_labels["current"].setText(f"{status['current_ma']}")
            self.status_labels["temperature"].setText(f"{status['temperature_c']:.1f}")
            self.status_labels["power_status"].setText(status["power_control"].name)
            self.status_labels["reference"].setText(status["reference_source"].name)
            
            if self.is_calibration_module:
                self.status_labels["tx_frequency"].setText(f"{status['tx_frequency_mhz']:.2f}")
                
            # 根据状态设置颜色
            if status["module_status"] == ModuleStatus.NORMAL:
                self.status_labels["module_status"].setStyleSheet("QLabel { font-weight: bold; font-size: 14px; color: white; background-color: #34a853; padding: 5px; border-radius: 4px; }")
            elif status["module_status"] == ModuleStatus.FAULT:
                self.status_labels["module_status"].setStyleSheet("QLabel { font-weight: bold; font-size: 14px; color: white; background-color: #ea4335; padding: 5px; border-radius: 4px; }")
            else:
                self.status_labels["module_status"].setStyleSheet("QLabel { font-weight: bold; font-size: 14px; color: #5f6368; background-color: #f1f3f4; padding: 5px; border-radius: 4px; }")
                
            if status["lo1_lock_status"] == ModuleStatus.NORMAL:
                self.status_labels["lo1_lock"].setStyleSheet("QLabel { font-weight: bold; font-size: 14px; color: white; background-color: #34a853; padding: 5px; border-radius: 4px; }")
            elif status["lo1_lock_status"] == ModuleStatus.FAULT:
                self.status_labels["lo1_lock"].setStyleSheet("QLabel { font-weight: bold; font-size: 14px; color: white; background-color: #ea4335; padding: 5px; border-radius: 4px; }")
            else:
                self.status_labels["lo1_lock"].setStyleSheet("QLabel { font-weight: bold; font-size: 14px; color: #5f6368; background-color: #f1f3f4; padding: 5px; border-radius: 4px; }")
                
            if status["lo2_lock_status"] == ModuleStatus.NORMAL:
                self.status_labels["lo2_lock"].setStyleSheet("QLabel { font-weight: bold; font-size: 14px; color: white; background-color: #34a853; padding: 5px; border-radius: 4px; }")
            elif status["lo2_lock_status"] == ModuleStatus.FAULT:
                self.status_labels["lo2_lock"].setStyleSheet("QLabel { font-weight: bold; font-size: 14px; color: white; background-color: #ea4335; padding: 5px; border-radius: 4px; }")
            else:
                self.status_labels["lo2_lock"].setStyleSheet("QLabel { font-weight: bold; font-size: 14px; color: #5f6368; background-color: #f1f3f4; padding: 5px; border-radius: 4px; }")
                
            # 温度警告
            temp = status['temperature_c']
            if temp > 70:
                self.status_labels["temperature"].setStyleSheet("QLabel { font-weight: bold; font-size: 14px; color: white; background-color: #ea4335; padding: 5px; border-radius: 4px; }")
            elif temp > 60:
                self.status_labels["temperature"].setStyleSheet("QLabel { font-weight: bold; font-size: 14px; color: #f9ab00; background-color: #fef7e0; padding: 5px; border-radius: 4px; }")
            else:
                self.status_labels["temperature"].setStyleSheet("QLabel { font-weight: bold; font-size: 14px; color: #1a73e8; background-color: #e8f0fe; padding: 5px; border: 1px solid #dadce0; border-radius: 4px; }")
                
        except Exception as e:
            print(f"更新状态显示错误: {e}")
            
    def update_status_text(self, message):
        """更新状态文本"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.status_text.append(f"[{timestamp}] {message}")
        
        # 限制行数
        if self.status_text.document().blockCount() > 50:
            cursor = self.status_text.textCursor()
            cursor.movePosition(QTextCursor.Start)
            cursor.select(QTextCursor.BlockUnderCursor)
            cursor.removeSelectedText()
            
    def append_log(self, message):
        """添加日志 - 线程安全版本"""
        # 使用 QMetaObject.invokeMethod 确保在主线程中执行
        if QThread.currentThread() != self.thread():
            # 从其他线程调用，使用信号槽机制
            QMetaObject.invokeMethod(self, "_append_log_impl",
                                    Qt.QueuedConnection,
                                    Q_ARG(str, message))
        else:
            # 已在主线程，直接执行
            self._append_log_impl(message)
    
    @pyqtSlot(str)
    def _append_log_impl(self, message):
        """实际的日志添加实现 - 仅在主线程中执行"""
        try:
            # 检查控件是否有效
            if not self.log_text or not self.log_text.isVisible():
                return
                
            self.log_text.append(message)
            
            # 自动滚动
            if self.auto_scroll_check and self.auto_scroll_check.isChecked():
                cursor = self.log_text.textCursor()
                cursor.movePosition(QTextCursor.End)
                self.log_text.setTextCursor(cursor)
                
            # 限制日志行数
            doc = self.log_text.document()
            if doc and doc.blockCount() > 500:
                cursor = self.log_text.textCursor()
                cursor.movePosition(QTextCursor.Start)
                cursor.select(QTextCursor.BlockUnderCursor)
                cursor.removeSelectedText()
                cursor.deleteChar()  # 删除换行符
        except Exception as e:
            print(f"日志添加错误: {e}")
            
    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            # 先断开信号连接，避免关闭时仍在处理信号
            if self.query_thread:
                self.query_thread.status_received.disconnect()
                self.query_thread.log_message.disconnect()
                self.query_thread.stop()
                self.query_thread = None
                
            # 关闭CAN设备
            if self.can:
                self.can.close_device()
                
        except Exception as e:
            print(f"关闭窗口错误: {e}")
        finally:
            event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle("Fusion")
    
    # 创建并显示主窗口
    window = ConverterControlWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()