"""
变频模块CAN通信协议实现
基于doc/通信协议.md实现的变频模块控制和查询协议
"""

import struct
import time
from datetime import datetime
from enum import Enum


class ModuleCommand(Enum):
    """命令码定义"""
    QUERY = 0x06          # 查询命令
    CONTROL_RF = 0x01     # 参数控制1（RF或RRF频率）
    CONTROL_ATT = 0x02    # 参数控制2（衰减、本振输出、加去电）
    CONTROL_ERF = 0x03    # 参数控制3（ERF发射频率，仅校零变频模块）


class ModuleStatus(Enum):
    """模块状态定义"""
    FAULT = 0x01      # 故障
    NORMAL = 0x02     # 正常
    RESERVED = 0x00   # 保留


class LOOutput(Enum):
    """本振测试输出选择"""
    LO1 = 0x01
    LO2 = 0x02
    RESERVED = 0x00


class PowerControl(Enum):
    """模块加去电控制"""
    POWER_OFF = 0x01         # 模块整体下电
    POWER_ON = 0x02          # 上电
    PARTIAL_OFF = 0x03       # 其余部分下电，晶振不下电


class ReferenceSource(Enum):
    """参考源"""
    INTERNAL = 0x01   # 内参考
    EXTERNAL = 0x02   # 外参考
    RESERVED = 0x00


class ConverterModuleProtocol:
    """变频模块协议处理器"""
    
    def __init__(self, can_interface):
        self.can = can_interface
        self.log_callback = None
        self.status_callback = None
        
    def set_log_callback(self, callback):
        """设置日志回调函数"""
        self.log_callback = callback
        
    def set_status_callback(self, callback):
        """设置状态回调函数"""
        self.status_callback = callback
        
    def _log(self, message):
        """输出日志信息"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        log_msg = f"[{timestamp}] {message}"
        if self.log_callback:
            self.log_callback(log_msg)
        else:
            print(log_msg)
            
    def _status(self, message):
        """输出状态信息"""
        if self.status_callback:
            self.status_callback(message)
            
    def _frequency_to_bcd(self, freq_mhz):
        """
        将频率转换为BCD编码
        协议格式: AABB.CCDDEE -> [0x00, 0xAA, 0xBB, 0xCC, 0xDD, 0xEE]
        例: 2230.567800MHz -> [0x00, 0x22, 0x30, 0x56, 0x78, 0x00]
        例: 2018.120000MHz -> [0x00, 0x20, 0x18, 0x12, 0x00, 0x00]
        例: 2230.60MHz -> [0x00, 0x22, 0x30, 0x60, 0x00, 0x00]
        
        Args:
            freq_mhz: 频率值（MHz）
            
        Returns:
            list: 6字节BCD编码
        """
        # 分离整数和小数部分
        freq_int = int(freq_mhz)
        freq_frac = int(round((freq_mhz - freq_int) * 1000000))  # 6位小数
        
        # 处理整数部分（最多4位数）
        thousands = freq_int // 1000
        hundreds = (freq_int // 100) % 10
        tens = (freq_int // 10) % 10
        ones = freq_int % 10
        
        # 处理小数部分（6位）
        # 将小数部分格式化为6位字符串，不足用0填充
        frac_str = f"{freq_frac:06d}"
        
        # 组装BCD字节 - 按照协议格式
        bcd_bytes = [
            0x00,  # 第一个字节固定为0x00
            (thousands << 4) | hundreds,  # 千位和百位
            (tens << 4) | ones,           # 十位和个位
            (int(frac_str[0]) << 4) | int(frac_str[1]),  # 第1、2位小数
            (int(frac_str[2]) << 4) | int(frac_str[3]),  # 第3、4位小数
            (int(frac_str[4]) << 4) | int(frac_str[5])   # 第5、6位小数
        ]
        
        return bcd_bytes
        
    def _bcd_to_frequency(self, bcd_bytes):
        """
        将BCD编码转换为频率
        协议格式: [0x00, 0xAA, 0xBB, 0xCC, 0xDD, 0xEE] -> AABB.CCDDEE MHz
        例: [0x00, 0x22, 0x30, 0x56, 0x78, 0x00] -> 2230.567800MHz
        例: [0x00, 0x20, 0x18, 0x12, 0x00, 0x00] -> 2018.120000MHz
        
        Args:
            bcd_bytes: 6字节BCD编码
            
        Returns:
            float: 频率值（MHz）
        """
        if len(bcd_bytes) < 6:
            return 0.0
            
        # 第一个字节是0x00，跳过
        # 解析各个字节
        thousands = (bcd_bytes[1] >> 4) & 0x0F
        hundreds = bcd_bytes[1] & 0x0F
        tens = (bcd_bytes[2] >> 4) & 0x0F
        ones = bcd_bytes[2] & 0x0F
        
        # 小数部分
        frac_100k = (bcd_bytes[3] >> 4) & 0x0F
        frac_10k = bcd_bytes[3] & 0x0F
        frac_1k = (bcd_bytes[4] >> 4) & 0x0F
        frac_100 = bcd_bytes[4] & 0x0F
        frac_10 = (bcd_bytes[5] >> 4) & 0x0F
        frac_1 = bcd_bytes[5] & 0x0F
        
        # 组装频率值
        freq_int = thousands * 1000 + hundreds * 100 + tens * 10 + ones
        freq_frac = (frac_100k * 100000 + frac_10k * 10000 + frac_1k * 1000 + 
                    frac_100 * 100 + frac_10 * 10 + frac_1) / 1000000.0
        
        return freq_int + freq_frac
            
    def _attenuation_to_bcd(self, att_db):
        """
        将衰减值转换为BCD编码
        
        Args:
            att_db: 衰减值（dB），整数
            
        Returns:
            list: 2字节BCD编码
        """
        # 确保是整数
        att_int = int(att_db)
        
        # 转换为BCD编码，格式为XX.00（小数部分固定为00）
        tens = att_int // 10
        ones = att_int % 10
        
        bcd_bytes = [
            (tens << 4) | ones,  # 整数部分
            0x00  # 小数部分固定为00
        ]
        
        return bcd_bytes
        
    def _bcd_to_attenuation(self, bcd_bytes):
        """
        将BCD编码转换为衰减值
        
        Args:
            bcd_bytes: 2字节BCD编码
            
        Returns:
            float: 衰减值（dB）
        """
        if len(bcd_bytes) < 2:
            return 0.0
            
        # 从BCD解码
        high = ((bcd_bytes[0] >> 4) & 0x0F) * 10 + (bcd_bytes[0] & 0x0F)
        low = ((bcd_bytes[1] >> 4) & 0x0F) * 10 + (bcd_bytes[1] & 0x0F)
        
        # 转换为dB（0.1dB单位）
        att_int = high * 100 + low
        return att_int / 10.0
        
    def _build_extended_frame_id(self, module_addr, data_num, total_num, cmd):
        """
        构建扩展帧ID
        
        Args:
            module_addr: 模块地址（1-16）
            data_num: 数据段顺序号
            total_num: 总数据段数
            cmd: 命令码
            
        Returns:
            int: 29位扩展帧ID
        """
        # 扩展帧ID = (MAB << 24) | (DateNum << 16) | (SUMD << 8) | CMD
        ext_id = (module_addr << 24) | (data_num << 16) | (total_num << 8) | cmd
        return ext_id
        
    def send_control_rf(self, module_addr, freq_mhz, is_calibration=False):
        """
        发送射频频率控制命令
        
        Args:
            module_addr: 模块地址（1-16）
            freq_mhz: 频率（MHz）
            is_calibration: 是否为校零变频模块（True则为接收频率RRF）
            
        Returns:
            bool: 发送成功返回True
        """
        try:
            # 转换频率为BCD编码
            freq_bcd = self._frequency_to_bcd(freq_mhz)
            
            # 构建数据段（6字节频率 + 2字节填充）
            data = freq_bcd + [0x00, 0x00]
            
            # 构建扩展帧ID
            ext_id = self._build_extended_frame_id(
                module_addr, 1, 1, ModuleCommand.CONTROL_RF.value
            )
            
            # 发送CAN消息
            if is_calibration:
                self._log(f"发送校零变频接收频率控制: 地址={module_addr}, RRF={freq_mhz}MHz")
            else:
                self._log(f"发送射频频率控制: 地址={module_addr}, RF={freq_mhz}MHz")
                
            success = self.can.send_extended_frame(ext_id, data[:8])
            
            if success:
                self._status(f"频率设置成功: {freq_mhz}MHz")
            else:
                self._status(f"频率设置失败")
                
            return success
            
        except Exception as e:
            self._log(f"发送频率控制失败: {e}")
            return False
            
    def send_control_att(self, module_addr, att_db, lo_output, power_control):
        """
        发送衰减、本振输出、加去电控制命令
        
        Args:
            module_addr: 模块地址（1-16）
            att_db: 衰减值（dB）
            lo_output: 本振输出选择（LOOutput枚举）
            power_control: 加去电控制（PowerControl枚举）
            
        Returns:
            bool: 发送成功返回True
        """
        try:
            # 转换衰减为BCD编码
            att_bcd = self._attenuation_to_bcd(att_db)
            
            # 构建数据段
            data = att_bcd + [lo_output.value, power_control.value] + [0x00] * 4
            
            # 构建扩展帧ID
            ext_id = self._build_extended_frame_id(
                module_addr, 1, 1, ModuleCommand.CONTROL_ATT.value
            )
            
            # 发送CAN消息
            self._log(f"发送参数控制: 地址={module_addr}, 衰减={att_db}dB, "
                     f"本振输出={lo_output.name}, 电源={power_control.name}")
                     
            success = self.can.send_extended_frame(ext_id, data[:8])
            
            if success:
                self._status(f"参数设置成功")
            else:
                self._status(f"参数设置失败")
                
            return success
            
        except Exception as e:
            self._log(f"发送参数控制失败: {e}")
            return False
            
    def send_control_erf(self, module_addr, freq_mhz):
        """
        发送发射频率控制命令（仅校零变频模块）
        
        Args:
            module_addr: 模块地址（1-16）
            freq_mhz: 发射频率（MHz）
            
        Returns:
            bool: 发送成功返回True
        """
        try:
            # 转换频率为BCD编码
            freq_bcd = self._frequency_to_bcd(freq_mhz)
            
            # 构建数据段
            data = freq_bcd + [0x00, 0x00]
            
            # 构建扩展帧ID
            ext_id = self._build_extended_frame_id(
                module_addr, 1, 1, ModuleCommand.CONTROL_ERF.value
            )
            
            # 发送CAN消息
            self._log(f"发送校零变频发射频率控制: 地址={module_addr}, ERF={freq_mhz}MHz")
            
            success = self.can.send_extended_frame(ext_id, data[:8])
            
            if success:
                self._status(f"发射频率设置成功: {freq_mhz}MHz")
            else:
                self._status(f"发射频率设置失败")
                
            return success
            
        except Exception as e:
            self._log(f"发送发射频率控制失败: {e}")
            return False
            
    def send_query(self, module_addr):
        """
        发送查询命令
        
        Args:
            module_addr: 模块地址（1-16）
            
        Returns:
            bool: 发送成功返回True
        """
        try:
            # 构建扩展帧ID
            ext_id = self._build_extended_frame_id(
                module_addr, 1, 1, ModuleCommand.QUERY.value
            )
            
            # 构建数据段（全0）
            data = [0x00] * 8
            
            # 发送CAN消息
            self._log(f"发送查询命令: 地址={module_addr}")
            
            return self.can.send_extended_frame(ext_id, data)
            
        except Exception as e:
            self._log(f"发送查询命令失败: {e}")
            return False
            
    def parse_query_response(self, frames):
        """
        解析查询响应数据
        
        Args:
            frames: 接收到的4个CAN帧数据列表
            
        Returns:
            dict: 解析后的状态数据，如果解析失败返回None
        """
        try:
            if len(frames) < 4:
                self._log(f"查询响应帧数不足: {len(frames)}/4")
                return None
                
            # 合并4帧数据
            data = []
            for frame in frames:
                # 每帧取8字节数据
                data.extend(frame['data'][:8])
                
            if len(data) < 32:
                self._log(f"查询响应数据长度不足: {len(data)}/32")
                return None
                
            # 解析数据元素
            status = {
                'module_status': ModuleStatus(data[0]) if data[0] in [e.value for e in ModuleStatus] else ModuleStatus.RESERVED,
                'lo1_lock_status': ModuleStatus(data[1]) if data[1] in [e.value for e in ModuleStatus] else ModuleStatus.RESERVED,
                'lo2_lock_status': ModuleStatus(data[2]) if data[2] in [e.value for e in ModuleStatus] else ModuleStatus.RESERVED,
                'lo_output': LOOutput(data[3]) if data[3] in [e.value for e in LOOutput] else LOOutput.RESERVED,
                'attenuation_db': self._bcd_to_attenuation(data[4:6]),
                'rf_frequency_mhz': self._bcd_to_frequency(data[6:12]),
                'input_power_dbm': self._parse_power(data[12:15]),
                'output_power_dbm': self._parse_power(data[15:18]),
                'voltage_v': self._bcd_to_voltage(data[18:20]),
                'current_ma': self._bcd_to_current(data[20:22]),
                'temperature_c': self._bcd_to_temperature(data[22:24]),
                'power_control': PowerControl(data[24]) if data[24] in [e.value for e in PowerControl] else PowerControl.POWER_OFF,
                'reference_source': ReferenceSource(data[25]) if data[25] in [e.value for e in ReferenceSource] else ReferenceSource.RESERVED,
                'tx_frequency_mhz': self._bcd_to_frequency(data[26:32])  # 仅校零变频模块有效
            }
            
            # 输出状态信息
            self._status(f"模块状态: {status['module_status'].name}")
            self._status(f"本振1锁定: {status['lo1_lock_status'].name}")
            self._status(f"本振2锁定: {status['lo2_lock_status'].name}")
            self._status(f"频率: {status['rf_frequency_mhz']:.6f} MHz")
            self._status(f"衰减: {status['attenuation_db']:.1f} dB")
            self._status(f"温度: {status['temperature_c']:.1f} ℃")
            
            return status
            
        except Exception as e:
            self._log(f"解析查询响应失败: {e}")
            return None
            
    def _parse_power(self, power_bytes):
        """解析功率值"""
        if len(power_bytes) < 3:
            return 0.0
            
        sign = 1 if power_bytes[0] == 0x01 else -1
        value = ((power_bytes[1] >> 4) & 0x0F) * 10 + (power_bytes[1] & 0x0F)
        decimal = ((power_bytes[2] >> 4) & 0x0F) * 10 + (power_bytes[2] & 0x0F)
        
        return sign * (value + decimal / 100.0)
        
    def _bcd_to_voltage(self, voltage_bytes):
        """BCD编码转换为电压"""
        if len(voltage_bytes) < 2:
            return 0.0
            
        integer = ((voltage_bytes[0] >> 4) & 0x0F) * 10 + (voltage_bytes[0] & 0x0F)
        decimal = ((voltage_bytes[1] >> 4) & 0x0F) * 10 + (voltage_bytes[1] & 0x0F)
        
        return integer + decimal / 100.0
        
    def _bcd_to_current(self, current_bytes):
        """BCD编码转换为电流"""
        if len(current_bytes) < 2:
            return 0
            
        high = ((current_bytes[0] >> 4) & 0x0F) * 10 + (current_bytes[0] & 0x0F)
        low = ((current_bytes[1] >> 4) & 0x0F) * 10 + (current_bytes[1] & 0x0F)
        
        return high * 100 + low
        
    def _bcd_to_temperature(self, temp_bytes):
        """BCD编码转换为温度"""
        if len(temp_bytes) < 2:
            return 0.0
            
        sign = 1 if temp_bytes[0] == 0x01 else -1
        value = ((temp_bytes[1] >> 4) & 0x0F) * 10 + (temp_bytes[1] & 0x0F)
        
        return sign * value