### 4.6 通信要求
与变频模块的通讯方式采用1路CAN总线通信。其通讯参数定义如下：
总线波特率：1Mbps
CAN通信信息帧为变频模块工作参数控制/查询命令扩展帧

#### 4.6.1 变频模块控制
控制命令帧由MCP发出，由变频模块接收。
变频模块控制命令帧定义参见表4。
##### 表4 变频模块控制命令帧描述表
| 扩展帧  | 扩展帧ID号 | 扩展帧ID号  | 扩展帧ID号 | 扩展帧ID号 | 数据段 |
| ---- | ------ | ------- | ------ | ------ | --- |
| 字段顺序 | 模块通信地址 | 数据段顺序号  | 总数据段   | 命令码    | 参数  |
| 字段标识 | MAB    | DateNum | SUMD   | CMD    | PRB |
| 填充内容 | 见说明    | 见说明     | 见说明    | 见说明    | 见说明 |
| 字节数  | 1B     | 1B      | 1B     | 1B     | 8B  |
说明：
	MAB：模块通信地址(1字节)，模块地址范围：1~16。
	SUMD：一个扩展帧数据段每次只能传输8B参数数据，如果参数个数N<=8, 该字节为1，参数数据传输一次完成；如果参数个数8<N<=16，该字节为2，参数数据传输要两次完成；依次类推，N每增加8，该字节加1，相应参数传输次数要加1次。
	DateNum：如果参数个数N<=8, 该字节为1；
			 如果参数个数8<N<=16 ，该字节为1时，表示参数顺序1-8;
			 该字节为2时，表示参数顺序9-16。
	CMD：0x06为查询命令。
		 OxO1为参数控制1（参数包含RF或RRF), RRF：校零变频接收频率；
		 Ox02为参数控制2（参数包含Att, LOSW, ConvPower）；
		 Ox03为参数控制3（参数包含ERF), ERF：校零变频发射频率。
	模块通信地址：模块根据地址线来采集地址。
	工作参数以BCD编码的方式表示。工作参数依次如下：
	RF--射频频率（6字节）：2230.567800MHz表示为：00 22 30 56 78 00 （频率分发方式见技术指标表）；
	Att--衰减（2字节）：2.1dB，表示为：02 10;
	LOSW--本振测试输出选择（1字节）LO1，表示为：01；LO2，表示为02；
	ConvPower--模块加去电控制（1字节）：上电，表示为：02；模块整体下电，表示为01；其余部分下电，晶振不下电，表示为03；
	举例：地址线接收的电平为0100（地址线4为低电平，地址线3为高电平，地址线2为低电平，地址线1为低电平），即地址为04；射频频率为12800.000MHz；衰减为9.2dB；本振测试输出选择LO2；模块上电。
	发送的命令为：
	04 01 01 01 01 28 00 00 00 00 00 00 //RF(6B)
	04 01 01 02 09 20 02 02 00 00 00 00 //Att(2B);LOSW(1B);ConvPower(1B);
	针对校零变频器举例：地址线接收的电平为0100（地址线4为低电平，地址线3为高电平，地址线2为低电平，地址线1为低电平），即地址为04；接收频率为2018.12MHz；衰减为9.2dB；本振测试输出选择LO2；模块上电；发射频率为2301.88MHz。
	发送的命令为：
	04 01 01 01 00 20 18 12 00 00 00 00 // RRF(6B)
	04 01 01 02 09 20 02 02 00 00 00 00 // Att(2B);LOSW(1B);ConvPower(1B); 
	04 01 01 03 00 23 01 88 00 00 00 00 // ERF(6B).
	备注：发送正在执行的频率码、衰减、加去电，此时模块频率码、衰 减、加去电需要再次执行。
#### 4.6.1 变频模块状态参数查询
查询命令帧由MCP定时50ms发出，由变频模块接收。
变频模块状态参数查询响应帧定义参见表5。
##### 表5 变频模块状态参数查询响应帧描述表
| 扩展帧  | 扩展帧ID号 | 扩展帧ID号  | 扩展帧ID号 | 扩展帧ID号 | 数据段     |
| ---- | ------ | ------- | ------ | ------ | ------- |
| 字段顺序 | 模块通信地址 | 数据段顺序号  | 总数据段   | 命令码    | 参数      |
| 字段标识 | MAB    | DateNum | SUMD   | CMD    | PRB     |
| 填充内容 | 见说明    | 见说明     | 见说明    | 见说明    | 见数据元素表8 |
| 字节数  | 1B     | 1B      | 1B     | 1B     | 8B      |
说明：
	MAB：模块通信地址(1字节)，模块地址从1开始。
	SUMD：一个扩展帧数据段每次只能传输8B参数数据，如果参数个数N<=8, 该字节为1，参数数据传输一次完成；如果参数个数8<N<=16，该字节为2，参数数据传输要两次完成；依次类推，N每增加8，该字节加1，相应参数传输次数要加1次。变频模块参数有32个Byte字节，所以该字节为4, 查询参数数据要四次传输完成。
	DateNum：如果参数个数N<=8, 该字节为1；
			 如果参数个数8<N<=16：
				 该字节为1时，表示参数顺序1-8;
				 该字节为2时，表示参数顺序9-16；
			 变频模块参数有32个Byte 字节：
				 该字节为1 时，表示参数顺序1-8;
				 该字节为2 时，表示参数顺序9-16;
				 该字节为3 时，表示参数顺序17-24;
				 该字节为4 时，表示参数顺序25-32。
	CMD：Ox06为查询命令。
	数据元素表见表6
##### 表6 变频模块状态数据元素表
| 数据元素             | 参数字节长度 | 单位    | 备注                                                                                |
| ---------------- | ------ | ----- | --------------------------------------------------------------------------------- |
| 变频模块状态           | 1B     |       | OxO1：故障<br>OxO2：正常<br>0x00：保留                                                     |
| 本振1 锁定状态         | 1B     |       | OxO1：故障<br>OxO2：正常<br>0x00：保留                                                     |
| 本振2 锁定状态         | 1B     |       | OxO1：故障<br>OxO2：正常<br>0x00：保留                                                     |
| 本振测试输出           | 1B     |       | OxO1：LO1<br>OxO2：LO2<br>0x00：保留                                                   |
| 衰减               | 2B     | O.1dB | 对照指标表，衰减量可控31dB或62dB                                                              |
| RF频率             | 6B     | Hz    | （对于校零变频模块，该数据元素表示接收频率）                                                            |
| 变频模块输入功率         | 3B     | dBm   | OxO1：+<br>OxO2：-<br>0x00：保留<br>后2Byte直接为BCD码的功率值<br>例如:<br>15.35dBm，表示为（2B）：15 35 |
| 变频模块输出功率         | 3B     | dBm   | OxO1：+<br>OxO2：-<br>0x00：保留<br>后2Byte直接为BCD码的功率值<br>例如:<br>15.35dBm，表示为（2B）：15 35 |
| 变频模块工作电压         | 2B     | V     |                                                                                   |
| 变频模块工作电流         | 2B     | mA    |                                                                                   |
| 温度               | 2B     | ℃     | OxO1：+<br>OxO2：-<br>0x00：保留                                                       |
| 模块加去电控制          | 1B     |       | OxO1：模块整体下电<br>Ox02：上电<br>Ox03: 其余部分下电，晶振不下电                                      |
| 参考源              | 1B     |       | OxO1：内参考<br>OxO2：外参考<br>OxO0：保留                                                   |
| 发射频率（仅对校零变频模块有效） | 6B     | Hz    |                                                                                   |
比如：
	地址线接收的电平为0100（地址线4为低电平，地址线3为高电平，地址线2为低电平，地址线1为低电平），即地址为04
	变频模块状态：正常，表示为（1B）：02
	本振1锁定状态：正常，表示为（1B）: 02
	本振2锁定状态：正常，表示为（1B）: 02
	本振测试输出：输出LO1，表示为（1B）: 01
	哀减：2.1dB，表示为（2B）：02 10
	RF频率：12800.000000MHz，表示为（6B）：01 28 00 00 00 00
	变颜模块输入功率幅度：-15.35dBm，表示为（2B）：02 15 35
	变频模块输出功率幅度：＋5.12dBm，表示为（2B）：01 05 12
	变频模块工作电压：11.85V，表示为（2B）：11 85
	变频模块工作电流：687mA，表示为（2B）：06 87
	温度：+53℃，表示为（2B）: 01 53
	变频模块加去电状态：上电，表示为（1B）：02
	参考源：内参考，表示为（1B）：01
	查询命令：04 01 01 06 00 00 00 00 00 00 00 00
	查询响应：04 01 04 06 02 02 02 01 02 10 01 28
			  04 02 04 06 00 00 00 00 02 15 35 01
			  04 03 04 06 05 12 11 85 06 87 01 53
			  04 04 04 06 02 01 00 00 00 00 00 00
	查询上报参数见表9，四次数据上报时，次数间隔时间为400us左右。
	针对校零变频模块，比如：地址线接收的电平为0100（地址线4为低电平，地址线3为高电平，地址线2为低电平，地址线1为低电平），即地址为04
	变频模块状态：正常，表示为（1B）：02
	本振1锁定状态：正常，表示为（1B）: 02
	本振2锁定状态：正常，表示为（1B）: 02
	本振测试输出：输出LO1，表示为（1B）: 01
	哀减：2.1dB，表示为（2B）：02 10
	接收频率：2018.120000MHz，表示为（6B）：00 20 18 12 00 00
	变颜模块输入功率幅度：-15.35dBm，表示为（2B）：02 15 35
	变频模块输出功率幅度：＋5.12dBm，表示为（2B）：01 05 12
	变频模块工作电压：11.85V，表示为（2B）：11 85
	变频模块工作电流：687mA，表示为（2B）：06 87
	温度：+53℃，表示为（2B）: 01 53
	变频模块加去电状态：上电，表示为（1B）：02
	参考源：内参考，表示为（1B）：01
	发射频率：2301.880000MHz，表示为（6B）：00 23 01 88 00 00
	查询命令：04 01 01 06 00 00 00 00 00 00 00 00
	查询响应：04 01 04 06 02 02 02 01 02 10 00 20
			  04 02 04 06 18 12 00 00 02 15 35 01
			  04 03 04 06 05 12 11 85 06 87 01 53
			  04 04 04 06 02 01 00 23 01 88 00 00