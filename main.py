"""
RF Module Test Application
Main entry point for the application
"""

# 首先设置DLL路径
from dll_loader import setup_dll_paths
setup_dll_paths()

import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
# from main_window import MainWindow  # Old UI
from main_window_v3 import MainWindowV3  # New UI for V0.3


def main():
    """Main application entry point"""
    # Set scale factor to 120% - must be before QApplication creation
    import os
    os.environ['QT_SCALE_FACTOR'] = '1.15'
    
    # Enable high DPI scaling
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    app = QApplication(sys.argv)
    app.setApplicationName("射频模块测试")
    app.setOrganizationName("射频测试")
    
    # Create and show main window - Using new V0.3 UI
    window = MainWindowV3()
    window.show()
    
    # Run application
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()