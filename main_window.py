"""
Main Window for RF Module Test Application
Provides the main GUI interface for controlling and monitoring RF modules
"""

import sys
import time
import threading
from ctypes import c_int
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QPushButton, QTextEdit, QGroupBox, QGridLayout,
                             QLabel, QComboBox, QMessageBox, QApplication,
                             QSplitter, QTabWidget, QSpinBox, QDateEdit,
                             QFrame, QSizePolicy)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, pyqtSlot, QDate
from PyQt5.QtGui import QFont, QTextCursor

from channel_control_widget import ChannelControlWidget
from usbcan2_interface import USBCAN2Interface
from rf_module_protocol import RFModuleProtocol
from can_message_queue import CANMessageQueue
from can_frame_assembler import CANFrameAssembler



class CANReceiveThread(QThread):
    """Background thread for CAN message reception"""
    
    message_received = pyqtSignal(list)  # Emit received data
    complete_message_received = pyqtSignal(str, list)  # Emit complete multi-frame messages
    
    def __init__(self, can_interface):
        super().__init__()
        self.can = can_interface
        self.running = False
        self.frame_assembler = CANFrameAssembler()
        
    def run(self):
        """Thread main loop"""
        self.running = True
        while self.running:
            
            # Try to receive multiple messages at once for better performance
            try:
                # Use ZCAN directly to receive up to 10 messages at once
                before_receive = time.time()
                msgs, count = self.can.zcan.Receive(self.can.channel_handle, 10, c_int(100))
                after_receive = time.time()
                
                if count > 0:
                    
                    # Process all received messages
                    for i in range(count):
                        frame = msgs[i]
                        data = []
                        for j in range(frame.frame.can_dlc):
                            data.append(frame.frame.data[j])
                        
                        # Emit single frame
                        self.message_received.emit(data)
                        
                        # Try to assemble multi-frame message
                        msg_type, complete_data = self.frame_assembler.add_frame(data)
                        if msg_type and complete_data:
                            # Emit complete message signal immediately
                            self.complete_message_received.emit(msg_type, complete_data)
            except Exception as e:
                # If error, fall back to single message receive
                success, data = self.can.receive_can_message(timeout=100)
                if success:
                    self.message_received.emit(data)
                    
                    msg_type, complete_data = self.frame_assembler.add_frame(data)
                    if msg_type and complete_data:
                        self.complete_message_received.emit(msg_type, complete_data)
                
    def stop(self):
        """Stop the thread"""
        self.running = False
        self.wait()  # Wait for thread to finish


class QueryWorker(QThread):
    """Worker thread for protocol queries to avoid blocking GUI"""
    
    # Signals for different query results
    module_info_ready = pyqtSignal(dict)
    bit_status_ready = pyqtSignal(dict)
    channel_params_ready = pyqtSignal(dict)
    query_failed = pyqtSignal(str)
    log_message = pyqtSignal(str, str)  # message, prefix
    
    def __init__(self, protocol):
        super().__init__()
        self.protocol = protocol
        self.query_type = None
        self.query_params = None
        
    def start_query(self, query_type, params=None):
        """Start a query operation"""
        self.query_type = query_type
        self.query_params = params
        self.start()
        
    def run(self):
        """Execute the query in background thread"""
        try:
            if self.query_type == 'module_info':
                self.log_message.emit("正在查询模块信息...", "")
                info = self.protocol.query_module_info()
                if info:
                    self.module_info_ready.emit(info)
                else:
                    self.query_failed.emit("查询模块信息失败")
                    
            elif self.query_type == 'bit_status':
                self.log_message.emit("正在查询BIT状态...", "")
                status = self.protocol.query_bit_status()
                if status:
                    self.bit_status_ready.emit(status)
                else:
                    self.query_failed.emit("查询BIT状态失败")
                    
            elif self.query_type == 'channel_params':
                group = self.query_params.get('group', 0)
                group_names = {0: "所有通道", 1: "通道 1-5", 2: "通道 6-10"}
                self.log_message.emit(f"正在查询{group_names[group]}参数...", "")
                params = self.protocol.query_channel_params(group)
                if params:
                    self.channel_params_ready.emit(params)
                else:
                    self.query_failed.emit(f"查询{group_names[group]}参数失败")
                    
        except Exception as e:
            self.query_failed.emit(f"查询异常: {str(e)}")


class MainWindow(QMainWindow):
    """Main application window"""
    
    def __init__(self):
        super().__init__()
        self.can = USBCAN2Interface()
        self.message_queue = CANMessageQueue()
        self.complete_message_queue = CANMessageQueue()  # Queue for complete messages
        self.protocol = RFModuleProtocol(self.can)
        self.protocol.set_message_queue(self.message_queue)  # Set message queue
        self.protocol.set_complete_message_queue(self.complete_message_queue)  # Set complete message queue
        self.protocol.set_log_callback(lambda msg: self.log(msg, prefix="PROTOCOL"))  # Set log callback
        self.protocol.set_status_log_callback(self.log_to_status)  # Set status log callback
        self.receive_thread = None
        self.query_worker = None
        self.connected = False
        
        self.init_ui()
        self.setWindowTitle("射频模块测试程序")
        self.resize(1200, 800)
        
        # 设置全局样式，互换边框线颜色
        self.setStyleSheet("""
            /* QGroupBox 使用深色边框（原QGroupBox的默认样式） */
            QGroupBox {
                border: 2px groove #808080;
                border-radius: 3px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            
            /* QFrame StyledPanel 使用浅色边框（原QFrame的默认样式） */
            QFrame[frameShape="6"] {
                border: 1px solid #c0c0c0;
                border-radius: 2px;
                padding: 2px;
            }
        """)
        
    def init_ui(self):
        """Initialize the user interface"""
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Connection controls
        conn_group = QGroupBox("连接设置")
        conn_layout = QHBoxLayout()
        
        conn_layout.addWidget(QLabel("通道:"))
        self.channel_combo = QComboBox()
        self.channel_combo.addItems(["通道 0", "通道 1"])
        conn_layout.addWidget(self.channel_combo)
        
        self.btn_connect = QPushButton("连接")
        self.btn_connect.clicked.connect(self.toggle_connection)
        conn_layout.addWidget(self.btn_connect)
        
        self.btn_test_conn = QPushButton("测试连接")
        self.btn_test_conn.clicked.connect(self.test_connection)
        self.btn_test_conn.setEnabled(False)
        conn_layout.addWidget(self.btn_test_conn)
        
        self.btn_test_send = QPushButton("测试发送")
        self.btn_test_send.clicked.connect(self.test_send)
        self.btn_test_send.setEnabled(False)
        conn_layout.addWidget(self.btn_test_send)
        
        conn_layout.addStretch()
        conn_group.setLayout(conn_layout)
        conn_group.setMaximumHeight(80)  # 限制高度
        main_layout.addWidget(conn_group)
        
        # Create splitter for main content
        splitter = QSplitter(Qt.Horizontal)
        
        # Left side: Controls
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)  # 移除默认边距
        left_layout.setSpacing(10)  # 保持控件间距
        
        # Channel controls (moved to top)
        self.channel_all = ChannelControlWidget("所有通道 (1-10)", 0x00)
        self.channel_all.send_clicked.connect(self.send_channel_params)
        left_layout.addWidget(self.channel_all)
        
        self.channel_1_5 = ChannelControlWidget("通道 1-5", 0x01)
        self.channel_1_5.send_clicked.connect(self.send_channel_params)
        left_layout.addWidget(self.channel_1_5)
        
        self.channel_6_10 = ChannelControlWidget("通道 6-10", 0x02)
        self.channel_6_10.send_clicked.connect(self.send_channel_params)
        left_layout.addWidget(self.channel_6_10)
        
        # Query buttons (moved to bottom)
        query_group = QGroupBox("查询操作")
        query_layout = QGridLayout()
        
        self.btn_query_info = QPushButton("查询模块信息")
        self.btn_query_info.clicked.connect(self.query_module_info)
        query_layout.addWidget(self.btn_query_info, 0, 0)
        
        self.btn_query_bit = QPushButton("查询BIT状态")
        self.btn_query_bit.clicked.connect(self.query_bit_status)
        query_layout.addWidget(self.btn_query_bit, 0, 1)
        
        self.btn_query_all = QPushButton("查询所有通道")
        self.btn_query_all.clicked.connect(lambda: self.query_channel_params(0))
        query_layout.addWidget(self.btn_query_all, 1, 0)
        
        self.btn_query_1_5 = QPushButton("查询通道 1-5")
        self.btn_query_1_5.clicked.connect(lambda: self.query_channel_params(1))
        query_layout.addWidget(self.btn_query_1_5, 1, 1)
        
        self.btn_query_6_10 = QPushButton("查询通道 6-10")
        self.btn_query_6_10.clicked.connect(lambda: self.query_channel_params(2))
        query_layout.addWidget(self.btn_query_6_10, 2, 0)
        
        self.btn_clear_log = QPushButton("清除日志")
        self.btn_clear_log.clicked.connect(self.clear_log)
        query_layout.addWidget(self.btn_clear_log, 2, 1)
        
        query_group.setLayout(query_layout)
        left_layout.addWidget(query_group)
        
        left_layout.addStretch()
        
        # Right side: Vertical layout with tabs and product info
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(0)  # 确保没有额外间距
        
        # Create vertical splitter for product info and tabs
        right_splitter = QSplitter(Qt.Vertical)
        
        # Top: Product info settings (moved to top)
        self.product_info_widget = self.create_product_info_widget()
        
        # Bottom: Tabs for different displays (moved to bottom)
        self.tab_widget = QTabWidget()
        
        # Log tab
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        self.tab_widget.addTab(self.log_text, "日志")
        
        # Status display tab
        self.status_text = QTextEdit()
        self.status_text.setReadOnly(True)
        self.status_text.setFont(QFont("Consolas", 10))
        self.tab_widget.addTab(self.status_text, "状态")
        
        # Add to vertical splitter (order changed)
        right_splitter.addWidget(self.product_info_widget)
        right_splitter.addWidget(self.tab_widget)
        right_splitter.setSizes([120, 480])  # 给产品信息适当的高度，不会太高
        
        right_layout.addWidget(right_splitter)
        
        # Add widgets to horizontal splitter
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setHandleWidth(10)  # 增加分隔条宽度，实际增加间距
        splitter.setSizes([450, 750])  # 给右侧更多空间
        
        main_layout.addWidget(splitter)
        
        # Set initial state
        self.set_controls_enabled(False)
        
    def create_product_info_widget(self):
        """Create product info settings widget"""
        group = QGroupBox("产品信息设置（调试功能）")
        group.setMinimumWidth(500)  # 增加最小宽度，确保当前值能一行显示
        layout = QGridLayout()
        layout.setSpacing(8)  # 适中的控件间距
        layout.setContentsMargins(10, 10, 10, 10)  # 减小边距
        layout.setHorizontalSpacing(15)  # 恢复合理的水平间距
        
        # Row 0: Manufacturer code and Production date
        layout.addWidget(QLabel("厂家代号:"), 0, 0)
        self.manufacturer_code = QSpinBox()
        self.manufacturer_code.setRange(0, 255)
        self.manufacturer_code.setValue(1)
        self.manufacturer_code.setMaximumWidth(80)
        layout.addWidget(self.manufacturer_code, 0, 1)
        
        layout.addWidget(QLabel("出厂日期:"), 0, 2)
        self.production_date = QDateEdit()
        self.production_date.setCalendarPopup(True)
        self.production_date.setDate(QDate.currentDate())
        self.production_date.setDisplayFormat("yyyy-MM-dd")
        self.production_date.setMaximumWidth(120)
        layout.addWidget(self.production_date, 0, 3, 1, 2)  # 跨两列以避免挤压
        
        # Row 1: Serial number components
        layout.addWidget(QLabel("序列号:"), 1, 0)
        
        # Create a horizontal layout for serial number components
        serial_widget = QWidget()
        serial_layout = QHBoxLayout(serial_widget)
        serial_layout.setContentsMargins(0, 0, 0, 0)
        serial_layout.setSpacing(8)  # 适中的间距
        
        self.serial_year = QSpinBox()
        self.serial_year.setRange(2000, 2099)
        self.serial_year.setValue(QDate.currentDate().year())
        self.serial_year.setMinimumWidth(85)  # 确保能显示4位数
        serial_layout.addWidget(self.serial_year)
        
        dash1 = QLabel("-")
        dash1.setAlignment(Qt.AlignCenter)
        serial_layout.addWidget(dash1)
        
        self.serial_batch = QSpinBox()
        self.serial_batch.setRange(1, 99)
        self.serial_batch.setValue(1)
        self.serial_batch.setMinimumWidth(50)
        serial_layout.addWidget(self.serial_batch)
        
        dash2 = QLabel("-")
        dash2.setAlignment(Qt.AlignCenter)
        serial_layout.addWidget(dash2)
        
        self.serial_number = QSpinBox()
        self.serial_number.setRange(1, 999)
        self.serial_number.setValue(1)
        self.serial_number.setMinimumWidth(65)  # 确保能显示3位数
        serial_layout.addWidget(self.serial_number)
        
        serial_layout.addStretch()  # 推到左边
        layout.addWidget(serial_widget, 1, 1, 1, 4)  # 调整跨列数
        
        # Add some spacing before buttons
        layout.setColumnMinimumWidth(4, 20)  # 添加一些间距
        
        # Buttons on the right side
        self.btn_read_info = QPushButton("读取")
        self.btn_read_info.clicked.connect(self.read_product_info)
        self.btn_read_info.setMinimumWidth(60)
        self.btn_read_info.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                font-weight: bold;
                border: none;
                padding: 5px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
        """)
        layout.addWidget(self.btn_read_info, 0, 5)
        
        self.btn_write_info = QPushButton("写入")
        self.btn_write_info.clicked.connect(self.write_product_info)
        self.btn_write_info.setMinimumWidth(60)
        self.btn_write_info.setStyleSheet("""
            QPushButton {
                background-color: #FF5722;
                color: white;
                font-weight: bold;
                border: none;
                padding: 5px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #E64A19;
            }
            QPushButton:pressed {
                background-color: #BF360C;
            }
        """)
        layout.addWidget(self.btn_write_info, 1, 5)
        
        # Current values display at bottom
        self.current_info_label = QLabel("当前值: 未查询")
        self.current_info_label.setWordWrap(False)  # 禁用自动换行，强制单行
        self.current_info_label.setStyleSheet("QLabel { color: #666; font-size: 9pt; }")  # 稍微减小字体
        self.current_info_label.setMinimumHeight(20)  # 设置最小高度
        self.current_info_label.setSizePolicy(QSizePolicy.Ignored, QSizePolicy.Fixed)  # 忽略内容宽度，固定高度
        layout.addWidget(self.current_info_label, 2, 0, 1, 6)  # 跨越到按钮列
        
        # Add stretch to push everything to the left
        layout.setColumnStretch(6, 1)  # 更新stretch列索引
        
        group.setLayout(layout)
        # 移除高度限制，让布局自然展开
        return group
        
    def set_controls_enabled(self, enabled):
        """Enable or disable all control widgets"""
        self.btn_query_info.setEnabled(enabled)
        self.btn_query_bit.setEnabled(enabled)
        self.btn_query_all.setEnabled(enabled)
        self.btn_query_1_5.setEnabled(enabled)
        self.btn_query_6_10.setEnabled(enabled)
        self.channel_all.set_enabled(enabled)
        self.channel_1_5.set_enabled(enabled)
        self.channel_6_10.set_enabled(enabled)
        self.btn_test_conn.setEnabled(enabled)
        self.btn_test_send.setEnabled(enabled)
        self.btn_read_info.setEnabled(enabled)
        self.btn_write_info.setEnabled(enabled)
        
    def toggle_connection(self):
        """Toggle CAN connection"""
        if not self.connected:
            # Connect
            channel = self.channel_combo.currentIndex()
            self.log(f"正在连接USBCAN-II设备，通道 {channel}...")
            
            if self.can.open_device(channel):
                self.connected = True
                self.btn_connect.setText("断开")
                self.channel_combo.setEnabled(False)
                self.set_controls_enabled(True)
                
                # Start receive thread
                self.receive_thread = CANReceiveThread(self.can)
                self.receive_thread.message_received.connect(self.on_can_message_received)
                self.receive_thread.complete_message_received.connect(self.on_complete_message_received)
                self.receive_thread.start()
                
                self.log("连接成功！")
                
                # Auto read product info after 500ms
                QTimer.singleShot(500, self.auto_read_product_info)
            else:
                QMessageBox.critical(self, "连接错误", 
                                   "无法连接到USBCAN-II设备")
        else:
            # Disconnect
            self.log("正在断开连接...")
            
            if self.receive_thread:
                self.receive_thread.stop()
                self.receive_thread = None
                
            self.can.close_device()
            self.connected = False
            self.btn_connect.setText("连接")
            self.channel_combo.setEnabled(True)
            self.set_controls_enabled(False)
            
            self.log("已断开连接")
            
    def query_module_info(self):
        """Query and display module information"""
        # Create and setup query worker if needed
        if hasattr(self, 'query_worker') and self.query_worker is not None:
            if self.query_worker.isRunning():
                self.log("查询正在进行中，请稍后...", error=True)
                return
            
        self.query_worker = QueryWorker(self.protocol)
        self.query_worker.module_info_ready.connect(self.on_module_info_ready)
        self.query_worker.query_failed.connect(self.on_query_failed)
        self.query_worker.log_message.connect(self.log)
        # Don't delete immediately - wait for cleanup
        self.query_worker.finished.connect(lambda: setattr(self, 'query_worker', None))
        
        # Start the query
        try:
            self.query_worker.start_query('module_info')
        except Exception as e:
            self.log(f"启动查询失败: {str(e)}", error=True)
        
    @pyqtSlot(dict)
    def on_module_info_ready(self, info):
        """Handle module info query result"""
        self.log("模块信息查询成功")
        
        # Update status display with parsed data
        self.status_text.clear()
        self.status_text.append("=== 模块信息 ===")
        self.status_text.append(f"制造商: {info.get('manufacturer_name', '未知')}")
        self.status_text.append(f"生产日期: {info['production_date']}")
        self.status_text.append(f"序列号: {info['serial_number']}")
        self.status_text.append(f"MARK地址: {info['mark_address']}")
        
        # Display frequency ranges for all oscillators
        if 'freq_ranges' in info:
            self.status_text.append("")
            self.status_text.append("频率范围:")
            self.status_text.append(f"  低段一本振: {info['freq_ranges']['low_osc1']['low_mhz']:.0f} - {info['freq_ranges']['low_osc1']['high_mhz']:.0f} MHz")
            self.status_text.append(f"  高段一本振: {info['freq_ranges']['high_osc1']['low_mhz']:.0f} - {info['freq_ranges']['high_osc1']['high_mhz']:.0f} MHz")
            self.status_text.append(f"  高段二本振: {info['freq_ranges']['high_osc2']['low_mhz']:.0f} - {info['freq_ranges']['high_osc2']['high_mhz']:.0f} MHz")
        else:
            # Fallback for compatibility
            self.status_text.append(f"频率范围: {info['freq_range']['low_mhz']:.1f} - {info['freq_range']['high_mhz']:.1f} MHz")
        
        self.status_text.append("")
        self.update_status_display()
        
        # Update product info display
        # 使用更紧凑的格式，避免换行
        info_text = f"当前值: 厂家{info.get('manufacturer_name', '未知')}, 出厂{info['production_date']}, 序列号{info['serial_number']}"
        self.current_info_label.setText(info_text)
        
        # Update input fields with current values
        self.update_product_info_fields(info)
            
    def query_bit_status(self):
        """Query and display BIT status"""
        # Create and setup query worker if needed
        if hasattr(self, 'query_worker') and self.query_worker is not None:
            if self.query_worker.isRunning():
                self.log("查询正在进行中，请稍后...", error=True)
                return
            
        self.query_worker = QueryWorker(self.protocol)
        self.query_worker.bit_status_ready.connect(self.on_bit_status_ready)
        self.query_worker.query_failed.connect(self.on_query_failed)
        self.query_worker.log_message.connect(self.log)
        # Don't delete immediately - wait for cleanup
        self.query_worker.finished.connect(lambda: setattr(self, 'query_worker', None))
        
        # Start the query
        try:
            self.query_worker.start_query('bit_status')
        except Exception as e:
            self.log(f"启动查询失败: {str(e)}", error=True)
        
    @pyqtSlot(dict)
    def on_bit_status_ready(self, status):
        """Handle BIT status query result"""
        self.log("BIT状态查询成功")
        
        # Update status display with parsed data
        self.status_text.clear()
        self.status_text.append("=== BIT状态 ===")
        self.status_text.append(f"温度: {status['temperature']}°C")
        self.status_text.append("")
        self.status_text.append("通道电压 (+5.5V检测):")
        for i, voltage in enumerate(status['channel_voltages']):
            if voltage is not None:
                self.status_text.append(f"  通道 {i+1}: {voltage:.1f}V")
            else:
                self.status_text.append(f"  通道 {i+1}: 无效")
        
        # Display -5V voltage if available
        if 'negative_5v' in status:
            if status['negative_5v'] is not None:
                self.status_text.append(f"\n-5V检测电压: {status['negative_5v']:.1f}V")
            else:
                self.status_text.append(f"\n-5V检测电压: 无效")
        
        # Display lock status if available
        if 'lock_status' in status:
            self.status_text.append("\n锁定状态:")
            lock_names = {
                'low_osc1_ch1': '低段一本振通道1',
                'low_osc1_ch2': '低段一本振通道2', 
                'low_osc2_locked': '低段二本振',
                'low_osc2_ref_normal': '低段二本振参考输入',
                'high_osc1_ch1': '高段一本振通道1',
                'high_osc2_ch1': '高段二本振通道1',
                'high_osc1_ch2': '高段一本振通道2',
                'high_osc2_ch2': '高段二本振通道2',
                'high_osc1_ch3': '高段一本振通道3',
                'high_osc2_ch3': '高段二本振通道3'
            }
            for key, name in lock_names.items():
                if key in status['lock_status']:
                    state = "正常" if key == 'low_osc2_ref_normal' else ("锁定" if status['lock_status'][key] else "未锁定")
                    self.status_text.append(f"  {name}: {state}")
        
        self.status_text.append("")
        self.update_status_display()
            
    def query_channel_params(self, group):
        """Query channel parameters"""
        # Create and setup query worker if needed
        if hasattr(self, 'query_worker') and self.query_worker is not None:
            if self.query_worker.isRunning():
                self.log("查询正在进行中，请稍后...", error=True)
                return
            
        self.query_worker = QueryWorker(self.protocol)
        self.query_worker.channel_params_ready.connect(self.on_channel_params_ready)
        self.query_worker.query_failed.connect(self.on_query_failed)
        self.query_worker.log_message.connect(self.log)
        # Don't delete immediately - wait for cleanup
        self.query_worker.finished.connect(lambda: setattr(self, 'query_worker', None))
        
        # Start the query
        try:
            self.query_worker.start_query('channel_params', {'group': group})
        except Exception as e:
            self.log(f"启动查询失败: {str(e)}", error=True)
        
    @pyqtSlot(dict)
    def on_channel_params_ready(self, params):
        """Handle channel params query result"""
        self.log("通道参数查询成功")
        
        # Clear status display
        self.status_text.clear()
        self.status_text.append("=== 通道参数 ===")
        
        # Display parsed data in status tab
        if 'group_1_5' in params:
            self.status_text.append("")
            self.status_text.append("通道 1-5:")
            self.status_text.append(f"  频率: {params['group_1_5']['freq_mhz']:.3f} MHz")
            self.status_text.append(f"  射频衰减: {params['group_1_5']['rf_att_db']} dB")
            self.status_text.append(f"  中频衰减: {params['group_1_5']['if_att_db']} dB")
            self.status_text.append(f"  带宽: {params['group_1_5']['bandwidth_str']}")
            self.status_text.append(f"  电源: {'开' if params['group_1_5']['power_on'] else '关'}")
            # Update UI controls
            self.channel_1_5.set_parameters(params['group_1_5'])
            
        if 'group_6_10' in params:
            self.status_text.append("")
            self.status_text.append("通道 6-10:")
            self.status_text.append(f"  频率: {params['group_6_10']['freq_mhz']:.3f} MHz")
            self.status_text.append(f"  射频衰减: {params['group_6_10']['rf_att_db']} dB")
            self.status_text.append(f"  中频衰减: {params['group_6_10']['if_att_db']} dB")
            self.status_text.append(f"  带宽: {params['group_6_10']['bandwidth_str']}")
            self.status_text.append(f"  电源: {'开' if params['group_6_10']['power_on'] else '关'}")
            # Update UI controls
            self.channel_6_10.set_parameters(params['group_6_10'])
            
        self.status_text.append("")
        self.update_status_display()
        
    @pyqtSlot(str)
    def on_query_failed(self, error_msg):
        """Handle query failure"""
        self.log(error_msg, error=True)
        # Debug: print protocol debug info if available
        if hasattr(self.protocol, '_last_debug'):
            self.log(f"调试信息: {self.protocol._last_debug}", prefix="DEBUG")
            
    def send_channel_params(self, params):
        """Send channel parameters"""
        group_names = {0: "所有通道", 1: "通道 1-5", 2: "通道 6-10"}
        group = params['channel_group']
        
        self.log(f"正在设置{group_names[group]}参数...")
        
        success = self.protocol.set_channel_params(
            params['channel_group'],
            params['freq_mhz'],
            params['rf_att_db'],
            params['if_att_db'],
            params['bandwidth_idx'],
            params['power_on']
        )
        
        if success:
            self.log("参数设置成功")
        else:
            self.log("参数设置失败", error=True)
            
    def on_can_message_received(self, data):
        """Handle received CAN message"""
        # Log raw message with timestamp
        hex_str = ' '.join(f'{b:02X}' for b in data)
        current_time = time.time()
        self.log(f"RX: {hex_str}", prefix="CAN")
        
        # Add to message queue for protocol handlers
        self.message_queue.put(data)
        # Debug: show queue status
        # self.log(f"DEBUG: Added to queue, queue empty: {self.message_queue.is_empty()}", prefix="QUEUE")
        
    def on_complete_message_received(self, msg_type, data):
        """Handle complete multi-frame message"""
        hex_str = ' '.join(f'{b:02X}' for b in data)
        current_time = time.time()
        # Only show completion message in log, actual data goes to status tab
        self.log(f"收到完整的 {msg_type} 响应 ({len(data)} 字节)")
        
        # Add complete message to queue
        self.complete_message_queue.put((msg_type, data))
        
    def update_status_display(self):
        """Update the status display tab"""
        # Auto-scroll to bottom
        cursor = self.status_text.textCursor()
        cursor.movePosition(QTextCursor.End)
        self.status_text.setTextCursor(cursor)
        
        # Switch to status tab to show the results
        self.tab_widget.setCurrentIndex(1)
        
    def log(self, message, error=False, prefix=""):
        """Add message to log"""
        # Handle when called as slot with (message, prefix) from signal
        if isinstance(error, str) and not prefix:
            prefix = error
            error = False
            
        if prefix:
            message = f"[{prefix}] {message}"
            
        if error:
            message = f"错误: {message}"
            
        self.log_text.append(message)
        
        # Auto-scroll to bottom
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.End)
        self.log_text.setTextCursor(cursor)
        
    def clear_log(self):
        """Clear the log text"""
        self.log_text.clear()
        self.log("日志已清除")
        
    def log_to_status(self, message):
        """Log human-readable message to status tab"""
        timestamp = time.strftime("%H:%M:%S")
        formatted_msg = f"[{timestamp}] {message}\n"
        self.status_text.append(formatted_msg)
        
        # Auto-scroll to bottom
        cursor = self.status_text.textCursor()
        cursor.movePosition(QTextCursor.End)
        self.status_text.setTextCursor(cursor)
        
        # Switch to status tab to show the message
        self.tab_widget.setCurrentIndex(1)
        
    def test_connection(self):
        """Test CAN connection"""
        self.log("=== 测试连接 ===")
        
        # Clear receive buffer
        try:
            self.can.clear_receive_buffer()
            self.log("✓ 接收缓冲区清空成功")
        except Exception as e:
            self.log(f"✗ 接收缓冲区清空失败: {e}", error=True)
        
        # Check if we can receive messages
        self.log("测试接收功能...")
        success, data = self.can.receive_can_message(timeout=100)
        if success:
            hex_str = ' '.join(f'{b:02X}' for b in data)
            self.log(f"✓ 接收到消息: {hex_str}")
        else:
            self.log("✓ 未接收到消息 (100ms内无数据)")
        
        self.log("✓ 连接测试完成")
        self.log("")
        
    def test_send(self):
        """Test CAN message sending"""
        self.log("=== 测试发送 ===")
        
        # Test message: Query module info
        test_msg = [0xF0, 0x00]
        hex_str = ' '.join(f'{b:02X}' for b in test_msg)
        self.log(f"发送测试消息: {hex_str}")
        
        # Simply send the message and let the receive thread handle the response
        if self.can.send_can_message(test_msg):
            self.log("✓ 消息发送成功")
            self.log("响应将在日志中显示...")
        else:
            self.log("✗ 消息发送失败")
        
        self.log("")
        
    def auto_read_product_info(self):
        """Auto read product info after connection"""
        self.log("自动读取产品信息...")
        self.query_module_info()
        
    def update_product_info_fields(self, info):
        """Update product info input fields with current values"""
        try:
            # Update manufacturer code
            self.manufacturer_code.setValue(info['manufacturer'])
            
            # Parse and update production date
            date_parts = info['production_date'].split('-')
            if len(date_parts) == 3:
                year = int(date_parts[0])
                month = int(date_parts[1])
                day = int(date_parts[2])
                self.production_date.setDate(QDate(year, month, day))
            
            # Parse and update serial number
            serial_parts = info['serial_number'].split('-')
            if len(serial_parts) == 3:
                self.serial_year.setValue(int(serial_parts[0]))
                self.serial_batch.setValue(int(serial_parts[1]))
                self.serial_number.setValue(int(serial_parts[2]))
                
            self.log("产品信息已更新到编辑框")
        except Exception as e:
            self.log(f"更新产品信息失败: {str(e)}", error=True)
        
    def read_product_info(self):
        """Read current product info from device"""
        self.log("正在读取产品信息...")
        self.query_module_info()  # Reuse existing query
        
    def write_product_info(self):
        """Write new product info to device"""
        # Show confirmation dialog
        reply = QMessageBox.warning(
            self, 
            "确认写入", 
            "警告：此操作将修改设备的产品信息！\n\n"
            "这是调试功能，请谨慎使用。\n"
            "确定要写入新的产品信息吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply != QMessageBox.Yes:
            return
            
        # Get values from UI
        manufacturer = self.manufacturer_code.value()
        prod_date = self.production_date.date()
        serial_year = self.serial_year.value()
        serial_batch = self.serial_batch.value()
        serial_num = self.serial_number.value()
        
        # Convert to BCD
        prod_year_bcd = self.to_bcd(prod_date.year())
        prod_month_bcd = self.to_bcd(prod_date.month())
        prod_day_bcd = self.to_bcd(prod_date.day())
        serial_year_bcd = self.to_bcd(serial_year)
        serial_batch_bcd = self.to_bcd(serial_batch)
        serial_num_bcd = self.to_bcd(serial_num)
        
        # Call protocol method
        success = self.protocol.set_product_info(
            manufacturer,
            prod_year_bcd, prod_month_bcd, prod_day_bcd,
            serial_year_bcd, serial_batch_bcd, serial_num_bcd
        )
        
        if success:
            self.log("产品信息写入成功", prefix="SUCCESS")
            # Read back to verify
            QTimer.singleShot(100, self.read_product_info)
        else:
            self.log("产品信息写入失败", error=True)
            
    def to_bcd(self, value):
        """Convert integer to BCD format"""
        if value >= 100:
            # For year (4 digits)
            return ((value // 1000) << 12) | \
                   (((value // 100) % 10) << 8) | \
                   (((value // 10) % 10) << 4) | \
                   (value % 10)
        else:
            # For month, day, batch (2 digits)
            return ((value // 10) << 4) | (value % 10)
        
    def closeEvent(self, event):
        """Handle window close event"""
        # Stop any running query worker
        if self.query_worker and self.query_worker.isRunning():
            self.query_worker.terminate()
            self.query_worker.wait()
            
        if self.connected:
            self.toggle_connection()
        event.accept()