"""
Main Window for RF Module Test Application V0.3
New UI design for multi-oscillator control system
"""

import sys
import time
import threading
from ctypes import c_int
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QPushButton, QTextEdit, QGroupBox, QGridLayout,
                             QLabel, QComboBox, QMessageBox, QApplication,
                             QSplitter, QTabWidget, QSpinBox, QDateEdit,
                             QFrame, QSizePolicy, QCheckBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, pyqtSlot, QDate
from PyQt5.QtGui import QFont, QTextCursor

from oscillator_control_widget import OscillatorControlWidget
from usbcan2_interface import USBCAN2Interface
from rf_module_protocol import RFModuleProtocol
from can_message_queue import CANMessageQueue
from can_frame_assembler import CANFrameAssembler


class CANReceiveThread(QThread):
    """Background thread for CAN message reception"""
    
    message_received = pyqtSignal(list)  # Emit received data
    complete_message_received = pyqtSignal(str, list)  # Emit complete multi-frame messages
    
    def __init__(self, can_interface):
        super().__init__()
        self.can = can_interface
        self.running = False
        self.frame_assembler = CANFrameAssembler()
        
    def run(self):
        """Thread main loop"""
        self.running = True
        while self.running:
            try:
                # Try to receive multiple messages at once for better performance
                before_receive = time.time()
                msgs, count = self.can.zcan.Receive(self.can.channel_handle, 10, c_int(100))
                after_receive = time.time()
                
                if count > 0:
                    # Process all received messages
                    for i in range(count):
                        frame = msgs[i]
                        data = []
                        for j in range(frame.frame.can_dlc):
                            data.append(frame.frame.data[j])
                        
                        # Emit single frame
                        self.message_received.emit(data)
                        
                        # Try to assemble multi-frame message
                        msg_type, complete_data = self.frame_assembler.add_frame(data)
                        if msg_type and complete_data:
                            # Emit complete message signal immediately
                            self.complete_message_received.emit(msg_type, complete_data)
            except Exception as e:
                # If error, fall back to single message receive
                success, data = self.can.receive_can_message(timeout=100)
                if success:
                    self.message_received.emit(data)
                    
                    msg_type, complete_data = self.frame_assembler.add_frame(data)
                    if msg_type and complete_data:
                        self.complete_message_received.emit(msg_type, complete_data)
                
    def stop(self):
        """Stop the thread"""
        self.running = False
        self.wait()  # Wait for thread to finish


class QueryWorker(QThread):
    """Worker thread for protocol queries to avoid blocking GUI"""
    
    # Signals for different query results
    module_info_ready = pyqtSignal(dict)
    bit_status_ready = pyqtSignal(dict)
    channel_params_ready = pyqtSignal(dict)
    params_set_complete = pyqtSignal(bool, int)  # success, channel_id
    query_failed = pyqtSignal(str)
    log_message = pyqtSignal(str, str)  # message, prefix
    status_message = pyqtSignal(str)  # status message for status tab
    
    def __init__(self, protocol):
        super().__init__()
        self.protocol = protocol
        self.query_type = None
        self.query_params = None
        # Replace protocol's status callback with signal emission
        self.original_status_callback = protocol.status_log_callback
        protocol.set_status_log_callback(self._emit_status_message)
    
    def _emit_status_message(self, message):
        """Emit status message signal instead of direct callback"""
        # Use QTimer to ensure UI update happens in main thread
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(0, lambda: self.status_message.emit(message))
        
    def start_query(self, query_type, params=None):
        """Start a query operation"""
        self.query_type = query_type
        self.query_params = params
        self.start()
        
    def run(self):
        """Execute the query in background thread"""
        try:
            if self.query_type == 'module_info':
                self.log_message.emit("正在查询模块信息...", "")
                info = self.protocol.query_module_info()
                if info:
                    self.module_info_ready.emit(info)
                else:
                    self.query_failed.emit("查询模块信息失败")
                    
            elif self.query_type == 'bit_status':
                self.log_message.emit("正在查询BIT状态...", "")
                status = self.protocol.query_bit_status()
                if status:
                    self.bit_status_ready.emit(status)
                else:
                    self.query_failed.emit("查询BIT状态失败")
                    
            elif self.query_type == 'channel_params':
                channel_id = self.query_params.get('channel_id', 0)
                self.log_message.emit(f"正在查询通道 0x{channel_id:02X} 参数...", "")
                params = self.protocol.query_channel_params(channel_id)
                if params:
                    self.channel_params_ready.emit(params)
                else:
                    self.query_failed.emit(f"查询通道参数失败")
                    
            elif self.query_type == 'set_params':
                # Set parameters in worker thread
                channel_id = self.query_params['channel_id']
                work_mode = self.query_params['work_mode']
                low_freq = self.query_params.get('low_freq', None)
                high1_freq = self.query_params.get('high1_freq', None)
                # Note: high2_freq removed in V0.5 protocol
                
                self.log_message.emit(f"正在设置通道 0x{channel_id:02X} 参数...", "")
                success = self.protocol.set_channel_params_v3(
                    channel_id, low_freq, high1_freq, work_mode
                )
                self.params_set_complete.emit(success, channel_id)
                
            elif self.query_type == 'write_product_info':
                # Write product info
                self.log_message.emit("正在写入产品信息...", "")
                success = self.protocol.write_product_info(self.query_params)
                self.params_set_complete.emit(success, 0)
                    
        except Exception as e:
            self.query_failed.emit(f"查询异常: {str(e)}")
        finally:
            # Restore original status callback when thread finishes
            if self.original_status_callback:
                self.protocol.set_status_log_callback(self.original_status_callback)


class MainWindowV3(QMainWindow):
    """Main application window for V0.3"""
    
    def __init__(self):
        super().__init__()
        self.can = USBCAN2Interface()
        self.message_queue = CANMessageQueue()
        self.complete_message_queue = CANMessageQueue()  # Queue for complete messages
        self.protocol = RFModuleProtocol(self.can)
        self.protocol.set_message_queue(self.message_queue)
        self.protocol.set_complete_message_queue(self.complete_message_queue)
        self.protocol.set_log_callback(lambda msg: self.log(msg, prefix="PROTOCOL"))
        # Set a default status_log_callback that's always safe to use
        self.protocol.set_status_log_callback(lambda msg: self.log_to_status(msg))
        self.receive_thread = None
        self.query_worker = None
        self.connected = False
        self.operation_in_progress = False  # Flag to prevent simultaneous operations
        
        self.init_ui()
        self.setWindowTitle("射频模块测试程序 V0.3 - 多本振控制系统")
        self.resize(1000, 650)  # Adjusted for switch control with all oscillators
        
    def init_ui(self):
        """Initialize the user interface"""
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Connection controls - compact single line
        conn_group = QGroupBox("连接设置")
        conn_group.setMaximumHeight(80)  # Limit height
        conn_layout = QHBoxLayout()
        conn_layout.setContentsMargins(10, 5, 5, 5)  # Increase left margin
        
        conn_layout.addWidget(QLabel("CAN通道:"))
        self.channel_combo = QComboBox()
        self.channel_combo.addItems(["通道 0", "通道 1"])
        self.channel_combo.setMaximumWidth(80)
        conn_layout.addWidget(self.channel_combo)
        
        conn_layout.addSpacing(20)  # Add space between channel and connect button
        
        self.btn_connect = QPushButton("连接")
        self.btn_connect.clicked.connect(self.toggle_connection)
        self.btn_connect.setMaximumWidth(60)
        conn_layout.addWidget(self.btn_connect)
        
        conn_layout.addSpacing(20)  # Same spacing as before connect button
        
        # Connection status indicator
        self.conn_indicator = QLabel("●")
        self.conn_indicator.setStyleSheet("color: #FF0000; font-size: 16px;")  # Red when disconnected
        self.conn_indicator.setToolTip("未连接")
        conn_layout.addWidget(self.conn_indicator)
        
        # Add a lot of stretch to push query buttons to the right
        conn_layout.addStretch(10)
        
        # Query buttons on the right with spacing
        self.btn_query_info = QPushButton("查询模块信息")
        self.btn_query_info.clicked.connect(self.query_module_info)
        self.btn_query_info.setEnabled(False)
        self.btn_query_info.setMaximumWidth(100)
        conn_layout.addWidget(self.btn_query_info)
        
        conn_layout.addSpacing(5)  # Add space between buttons
        
        self.btn_query_bit = QPushButton("查询BIT状态")
        self.btn_query_bit.clicked.connect(self.query_bit_status)
        self.btn_query_bit.setEnabled(False)
        self.btn_query_bit.setMaximumWidth(100)
        conn_layout.addWidget(self.btn_query_bit)
        
        conn_layout.addSpacing(5)  # Add space between buttons
        
        self.btn_query_all_params = QPushButton("查询所有参数")
        self.btn_query_all_params.clicked.connect(lambda: self.query_channel_params(0x00))
        self.btn_query_all_params.setEnabled(False)
        self.btn_query_all_params.setMaximumWidth(100)
        conn_layout.addWidget(self.btn_query_all_params)
        
        conn_layout.addSpacing(10)  # Add more space before checkbox
        
        # Auto query checkbox
        self.auto_query_checkbox = QCheckBox("设置后自动查询")
        self.auto_query_checkbox.setChecked(False)  # Default to off
        self.auto_query_checkbox.setToolTip("勾选后，设置参数会自动查询新值\n快速跳频时建议关闭")
        conn_layout.addWidget(self.auto_query_checkbox)
        
        conn_group.setLayout(conn_layout)
        main_layout.addWidget(conn_group)
        
        # Main content splitter
        splitter = QSplitter(Qt.Horizontal)
        
        # Left panel - Oscillator controls
        self.oscillator_widget = OscillatorControlWidget()
        self.oscillator_widget.send_params.connect(self.send_oscillator_params)
        self.oscillator_widget.setEnabled(False)
        splitter.addWidget(self.oscillator_widget)
        
        # Right panel - Product info and logs
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # Product info widget
        product_info_widget = self.create_product_info_widget()
        right_layout.addWidget(product_info_widget)
        
        # Switch control widget
        switch_control_widget = self.create_switch_control_widget()
        right_layout.addWidget(switch_control_widget)
        
        # Tab widget for logs
        self.tab_widget = QTabWidget()
        
        # Log tab with clear button
        log_widget = QWidget()
        log_layout = QVBoxLayout(log_widget)
        log_layout.setContentsMargins(0, 0, 0, 0)
        
        # Log toolbar
        log_toolbar = QHBoxLayout()
        log_toolbar.addStretch()
        
        self.btn_clear_log = QPushButton("清空日志")
        self.btn_clear_log.clicked.connect(self.clear_log)
        self.btn_clear_log.setMaximumWidth(100)
        log_toolbar.addWidget(self.btn_clear_log)
        
        log_layout.addLayout(log_toolbar)
        
        # Log text
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        log_layout.addWidget(self.log_text)
        
        self.tab_widget.addTab(log_widget, "通信日志")
        
        # Status tab
        status_widget = QWidget()
        status_layout = QVBoxLayout(status_widget)
        status_layout.setContentsMargins(0, 0, 0, 0)
        
        # Status toolbar
        status_toolbar = QHBoxLayout()
        status_toolbar.addStretch()
        
        self.btn_clear_status = QPushButton("清空状态")
        self.btn_clear_status.clicked.connect(self.clear_status)
        self.btn_clear_status.setMaximumWidth(100)
        status_toolbar.addWidget(self.btn_clear_status)
        
        status_layout.addLayout(status_toolbar)
        
        # Status text
        self.status_text = QTextEdit()
        self.status_text.setReadOnly(True)
        self.status_text.setFont(QFont("Consolas", 10))
        status_layout.addWidget(self.status_text)
        
        self.tab_widget.addTab(status_widget, "状态信息")
        
        right_layout.addWidget(self.tab_widget)
        splitter.addWidget(right_widget)
        
        # Set splitter sizes - balanced width
        splitter.setSizes([650, 550])
        main_layout.addWidget(splitter)
        
        # Status bar
        self.statusBar().showMessage("未连接")
        
    def create_product_info_widget(self):
        """Create product information widget"""
        group = QGroupBox("产品信息设置（调试功能）")
        group.setMaximumHeight(140)  # Increase height for better spacing
        
        layout = QGridLayout()
        layout.setContentsMargins(8, 8, 8, 8)  # Increase margins
        layout.setVerticalSpacing(8)  # Increase vertical spacing
        
        # Row 0: Manufacturer and production date
        layout.addWidget(QLabel("厂家代号:"), 0, 0)
        self.manufacturer_spin = QSpinBox()
        self.manufacturer_spin.setRange(0, 255)
        self.manufacturer_spin.setValue(0)
        self.manufacturer_spin.setMaximumWidth(80)
        layout.addWidget(self.manufacturer_spin, 0, 1)
        
        # Add spacer column for wider spacing
        layout.setColumnMinimumWidth(2, 30)
        
        layout.addWidget(QLabel("出厂日期:"), 0, 3)
        self.production_date = QDateEdit()
        self.production_date.setCalendarPopup(True)
        self.production_date.setDisplayFormat("yyyy-MM-dd")
        self.production_date.setDate(QDate.currentDate())
        self.production_date.setMaximumWidth(120)
        layout.addWidget(self.production_date, 0, 4)
        
        # Row 1: Serial number
        layout.addWidget(QLabel("序列号:"), 1, 0)
        
        # Create a horizontal layout for tighter serial number spacing
        serial_layout = QHBoxLayout()
        serial_layout.setSpacing(3)  # Very tight spacing
        
        # Serial year
        self.serial_year_spin = QSpinBox()
        self.serial_year_spin.setRange(2020, 2099)
        self.serial_year_spin.setValue(2025)
        self.serial_year_spin.setMaximumWidth(70)
        serial_layout.addWidget(self.serial_year_spin)
        
        serial_layout.addWidget(QLabel("-"))
        
        # Serial batch
        self.serial_batch_spin = QSpinBox()
        self.serial_batch_spin.setRange(1, 99)
        self.serial_batch_spin.setValue(1)
        self.serial_batch_spin.setMaximumWidth(50)
        serial_layout.addWidget(self.serial_batch_spin)
        
        serial_layout.addWidget(QLabel("-"))
        
        # Serial number
        self.serial_num_spin = QSpinBox()
        self.serial_num_spin.setRange(1, 999)
        self.serial_num_spin.setValue(1)
        self.serial_num_spin.setMaximumWidth(60)
        serial_layout.addWidget(self.serial_num_spin)
        
        serial_layout.addStretch()
        layout.addLayout(serial_layout, 1, 1, 1, 4)
        
        # Buttons - add more space and move right
        button_layout = QVBoxLayout()
        button_layout.setSpacing(5)  # Add space between buttons
        
        self.btn_read_product = QPushButton("读取")
        self.btn_read_product.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                font-weight: bold;
                padding: 3px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
            QPushButton:disabled {
                background-color: #CCCCCC;
            }
        """)
        self.btn_read_product.setMaximumHeight(28)
        self.btn_read_product.setMinimumWidth(80)  # Double the width
        self.btn_read_product.clicked.connect(self.read_product_info)
        self.btn_read_product.setEnabled(False)
        button_layout.addWidget(self.btn_read_product)
        
        self.btn_write_product = QPushButton("写入")
        self.btn_write_product.setStyleSheet("""
            QPushButton {
                background-color: #FF5722;
                color: white;
                font-weight: bold;
                padding: 3px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #E64A19;
            }
            QPushButton:pressed {
                background-color: #BF360C;
            }
            QPushButton:disabled {
                background-color: #CCCCCC;
            }
        """)
        self.btn_write_product.setMaximumHeight(28)
        self.btn_write_product.setMinimumWidth(80)  # Double the width
        self.btn_write_product.clicked.connect(self.write_product_info)
        self.btn_write_product.setEnabled(False)
        button_layout.addWidget(self.btn_write_product)
        
        # Add spacer before buttons to move them right
        layout.setColumnMinimumWidth(5, 20)
        layout.addLayout(button_layout, 0, 6, 2, 1)
        
        # Row 2: Current values display
        self.product_info_label = QLabel("当前值: 未读取")
        self.product_info_label.setWordWrap(True)
        self.product_info_label.setStyleSheet("color: #666; font-size: 11px;")
        layout.addWidget(self.product_info_label, 2, 0, 1, 7)
        
        # Add stretch to push everything to the left
        layout.setColumnStretch(7, 1)
        
        group.setLayout(layout)
        return group
        
    def create_switch_control_widget(self):
        """Create switch control widget for oscillator power control"""
        group = QGroupBox("开关控制（调试功能）")
        group.setMaximumHeight(200)
        
        layout = QGridLayout()
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setVerticalSpacing(6)
        
        # Create checkboxes for each oscillator channel
        self.switch_checkboxes = {}
        
        # Low oscillator 1
        layout.addWidget(QLabel("低段一本振1:"), 0, 0)
        self.switch_checkboxes['llo1_1'] = QCheckBox("通道1")
        self.switch_checkboxes['llo1_2'] = QCheckBox("通道2")
        layout.addWidget(self.switch_checkboxes['llo1_1'], 0, 1)
        layout.addWidget(self.switch_checkboxes['llo1_2'], 0, 2)
        
        # Low oscillator 2
        layout.addWidget(QLabel("低段二本振:"), 1, 0)
        self.switch_checkboxes['llo2_1'] = QCheckBox("通道1")
        self.switch_checkboxes['llo2_2'] = QCheckBox("通道2")
        layout.addWidget(self.switch_checkboxes['llo2_1'], 1, 1)
        layout.addWidget(self.switch_checkboxes['llo2_2'], 1, 2)
        
        # High oscillator 1
        layout.addWidget(QLabel("高段一本振:"), 2, 0)
        self.switch_checkboxes['hlo1_1'] = QCheckBox("通道1")
        self.switch_checkboxes['hlo1_2'] = QCheckBox("通道2")
        self.switch_checkboxes['hlo1_3'] = QCheckBox("通道3")
        layout.addWidget(self.switch_checkboxes['hlo1_1'], 2, 1)
        layout.addWidget(self.switch_checkboxes['hlo1_2'], 2, 2)
        layout.addWidget(self.switch_checkboxes['hlo1_3'], 2, 3)
        
        # High oscillator 2 - 调试功能保留硬件开关控制
        layout.addWidget(QLabel("高段二本振:"), 3, 0)
        self.switch_checkboxes['hlo2_1'] = QCheckBox("通道1")
        self.switch_checkboxes['hlo2_2'] = QCheckBox("通道2")
        self.switch_checkboxes['hlo2_3'] = QCheckBox("通道3")
        layout.addWidget(self.switch_checkboxes['hlo2_1'], 3, 1)
        layout.addWidget(self.switch_checkboxes['hlo2_2'], 3, 2)
        layout.addWidget(self.switch_checkboxes['hlo2_3'], 3, 3)
        
        # Control buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.btn_all_on = QPushButton("全部开启")
        self.btn_all_on.setMaximumWidth(80)
        self.btn_all_on.clicked.connect(self.set_all_switches_on)
        button_layout.addWidget(self.btn_all_on)
        
        self.btn_all_off = QPushButton("全部关闭")
        self.btn_all_off.setMaximumWidth(80)
        self.btn_all_off.clicked.connect(self.set_all_switches_off)
        button_layout.addWidget(self.btn_all_off)
        
        self.btn_apply_switch = QPushButton("应用设置")
        self.btn_apply_switch.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                font-weight: bold;
                padding: 5px 15px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:pressed {
                background-color: #E65100;
            }
            QPushButton:disabled {
                background-color: #CCCCCC;
            }
        """)
        self.btn_apply_switch.clicked.connect(self.apply_switch_settings)
        self.btn_apply_switch.setEnabled(False)
        button_layout.addWidget(self.btn_apply_switch)
        
        layout.addLayout(button_layout, 5, 0, 1, 4)
        
        # Add stretch
        layout.setColumnStretch(4, 1)
        
        group.setLayout(layout)
        return group
        
    def toggle_connection(self):
        """Toggle CAN connection"""
        if not self.connected:
            channel = self.channel_combo.currentIndex()
            if self.can.open_device(channel):
                self.connected = True
                self.btn_connect.setText("断开")
                self.statusBar().showMessage("已连接到CAN通道 " + str(channel))
                self.log(f"成功连接到CAN通道 {channel}")
                
                # Update connection indicator
                self.conn_indicator.setStyleSheet("color: #00FF00; font-size: 16px;")  # Green when connected
                self.conn_indicator.setToolTip("已连接")
                
                # Enable controls
                self.btn_query_info.setEnabled(True)
                self.btn_query_bit.setEnabled(True)
                self.btn_query_all_params.setEnabled(True)
                self.oscillator_widget.setEnabled(True)
                self.btn_read_product.setEnabled(True)
                self.btn_write_product.setEnabled(True)
                self.btn_apply_switch.setEnabled(True)
                
                # Start receive thread
                self.receive_thread = CANReceiveThread(self.can)
                self.receive_thread.message_received.connect(self.on_can_message_received)
                self.receive_thread.complete_message_received.connect(self.on_complete_message_received)
                self.receive_thread.start()
                
                # Auto query module info after connection
                QTimer.singleShot(500, self.query_module_info)
            else:
                self.log("连接失败", error=True)
                QMessageBox.critical(self, "错误", "无法打开CAN设备")
        else:
            # Disconnect
            if self.receive_thread:
                self.receive_thread.stop()
                self.receive_thread = None
                
            self.can.close_device()
            self.connected = False
            self.btn_connect.setText("连接")
            self.statusBar().showMessage("未连接")
            self.log("已断开连接")
            
            # Update connection indicator
            self.conn_indicator.setStyleSheet("color: #FF0000; font-size: 16px;")  # Red when disconnected
            self.conn_indicator.setToolTip("未连接")
            
            # Disable controls
            self.btn_query_info.setEnabled(False)
            self.btn_query_bit.setEnabled(False)
            self.btn_query_all_params.setEnabled(False)
            self.oscillator_widget.setEnabled(False)
            self.btn_read_product.setEnabled(False)
            self.btn_write_product.setEnabled(False)
            self.btn_apply_switch.setEnabled(False)
            
    def query_module_info(self):
        """Query and display module info"""
        if self.operation_in_progress:
            self.log("操作正在进行中，请稍后再试...")
            return
        
        self.operation_in_progress = True
        
        try:
            # Stop any ongoing query first
            self._stop_query_worker()
            
            self.query_worker = QueryWorker(self.protocol)
            self.query_worker.module_info_ready.connect(self.on_module_info_ready)
            self.query_worker.query_failed.connect(self.on_query_failed)
            self.query_worker.log_message.connect(self.log)
            self.query_worker.status_message.connect(self.log_to_status)
            self.query_worker.finished.connect(self.on_query_worker_finished)
            
            self.query_worker.start_query('module_info')
        except Exception as e:
            self.log(f"启动查询失败: {str(e)}", error=True)
            self.operation_in_progress = False
            if self.query_worker:
                self.query_worker.deleteLater()
                self.query_worker = None
            
    def query_bit_status(self):
        """Query and display BIT status"""
        if self.operation_in_progress:
            self.log("操作正在进行中，请稍后再试...")
            return
        
        self.operation_in_progress = True
        
        try:
            # Stop any ongoing query first
            self._stop_query_worker()
            
            self.query_worker = QueryWorker(self.protocol)
            self.query_worker.bit_status_ready.connect(self.on_bit_status_ready)
            self.query_worker.query_failed.connect(self.on_query_failed)
            self.query_worker.log_message.connect(self.log)
            self.query_worker.status_message.connect(self.log_to_status)
            self.query_worker.finished.connect(self.on_query_worker_finished)
            
            self.query_worker.start_query('bit_status')
        except Exception as e:
            self.log(f"启动查询失败: {str(e)}", error=True)
            self.operation_in_progress = False
            if self.query_worker:
                self.query_worker.deleteLater()
                self.query_worker = None
            
    def query_channel_params(self, channel_id):
        """Query channel parameters"""
        if self.operation_in_progress:
            self.log("操作正在进行中，请稍后再试...")
            return
        
        self.operation_in_progress = True
        
        try:
            # Stop any ongoing query first
            self._stop_query_worker()
            
            self.query_worker = QueryWorker(self.protocol)
            self.query_worker.channel_params_ready.connect(self.on_channel_params_ready)
            self.query_worker.query_failed.connect(self.on_query_failed)
            self.query_worker.log_message.connect(self.log)
            self.query_worker.status_message.connect(self.log_to_status)
            self.query_worker.finished.connect(self.on_query_worker_finished)
            
            self.query_worker.start_query('channel_params', {'channel_id': channel_id})
        except Exception as e:
            self.log(f"启动查询失败: {str(e)}", error=True)
            self.operation_in_progress = False
            if self.query_worker:
                self.query_worker.deleteLater()
                self.query_worker = None
            
    @pyqtSlot(dict)
    def on_module_info_ready(self, info):
        """Handle module info query result"""
        self.log("模块信息查询成功")
        
        # Update status display
        self.status_text.clear()
        self.status_text.append("=== 模块信息 ===")
        self.status_text.append(f"制造商: {info.get('manufacturer_name', '未知')}")
        self.status_text.append(f"生产日期: {info['production_date']}")
        self.status_text.append(f"序列号: {info['serial_number']}")
        self.status_text.append(f"MARK地址: {info['mark_address']}")
        
        # Display frequency ranges
        if 'freq_ranges' in info:
            self.status_text.append("")
            self.status_text.append("频率范围:")
            self.status_text.append(f"  低段一本振: {info['freq_ranges']['low_osc1']['low_mhz']:.0f} - {info['freq_ranges']['low_osc1']['high_mhz']:.0f} MHz")
            self.status_text.append(f"  高段一本振: {info['freq_ranges']['high_osc1']['low_mhz']:.0f} - {info['freq_ranges']['high_osc1']['high_mhz']:.0f} MHz")
        
        self.status_text.append("")
        self.update_status_display()
        
        # Update product info display
        self.update_product_info_display(info)
        
    @pyqtSlot(dict)
    def on_bit_status_ready(self, status):
        """Handle BIT status query result"""
        self.log("BIT状态查询成功")
        
        # Update status display
        self.status_text.clear()
        self.status_text.append("=== BIT状态 ===")
        self.status_text.append(f"温度: {status['temperature']}°C")
        self.status_text.append("")
        self.status_text.append("通道电压 (+5.5V检测):")
        for i, voltage in enumerate(status['channel_voltages']):
            if voltage is not None:
                self.status_text.append(f"  通道 {i+1}: {voltage:.1f}V")
            else:
                self.status_text.append(f"  通道 {i+1}: 无效")
        
        # Display -5V voltage
        if 'negative_5v' in status:
            if status['negative_5v'] is not None:
                self.status_text.append(f"\n-5V检测电压: {status['negative_5v']:.1f}V")
            else:
                self.status_text.append(f"\n-5V检测电压: 无效")
        
        # Update lock status in oscillator widget
        if 'lock_status' in status:
            self.oscillator_widget.update_lock_status(status['lock_status'])
            
            # Display in status
            self.status_text.append("\n锁定状态:")
            lock_names = {
                'low_osc1_ch1': '低段一本振通道1',
                'low_osc1_ch2': '低段一本振通道2',
                'low_osc2_locked': '低段二本振',
                'low_osc2_ref_normal': '低段二本振参考输入',
                'high_osc1_ch1': '高段一本振通道1',
                'high_osc1_ch2': '高段一本振通道2',
                'high_osc1_ch3': '高段一本振通道3'
            }
            for key, name in lock_names.items():
                if key in status['lock_status']:
                    if key == 'low_osc2_ref_normal':
                        state = "正常" if status['lock_status'][key] else "异常"
                    else:
                        state = "锁定" if status['lock_status'][key] else "未锁定"
                    self.status_text.append(f"  {name}: {state}")
        
        self.status_text.append("")
        self.update_status_display()
        
    @pyqtSlot(dict)
    def on_channel_params_ready(self, params):
        """Handle channel params query result"""
        self.log("通道参数查询成功")
        
        # Clean up retry state if this was from a retry
        if hasattr(self, 'pending_query_channel'):
            delattr(self, 'pending_query_channel')
        if hasattr(self, 'query_retry_count'):
            delattr(self, 'query_retry_count')
        
        # Update status display
        self.status_text.clear()
        self.status_text.append("=== 通道参数 ===")
        
        # Display parameters based on channel ID
        if params['channel_id'] == 0x00:
            # All channels - display in specified order
            self.status_text.append("\n0x01：低段一本振1通道参数")
            if params.get('low_osc1_freq_khz', 0xFFFFFFFF) != 0xFFFFFFFF:
                self.status_text.append(f"  频率: {params['low_osc1_freq_mhz']:.3f} MHz")
            
            self.status_text.append("\n0x02：低段一本振2通道参数")
            if 'low_osc1_ch2_freq_mhz' in params:
                self.status_text.append(f"  频率: {params['low_osc1_ch2_freq_mhz']:.3f} MHz")
            
            self.status_text.append("\n0x03：低段一本振开关/功分切换参数")
            self.status_text.append(f"  工作模式: {params['work_mode_str']}")
            
            self.status_text.append("\n0x04：高段一本振1通道参数")
            if params.get('high_osc1_freq_khz', 0xFFFFFFFF) != 0xFFFFFFFF:
                self.status_text.append(f"  频率: {params['high_osc1_freq_mhz']:.3f} MHz")
            
            self.status_text.append("\n0x05：保留")
            if params.get('high_osc2_freq_khz', 0xFFFFFFFF) != 0xFFFFFFFF:
                self.status_text.append(f"  频率: {params['high_osc2_freq_mhz']:.3f} MHz")
            
            self.status_text.append("\n0x06：高段一本振2通道参数")
            if 'high_osc1_ch2_freq_mhz' in params:
                self.status_text.append(f"  频率: {params['high_osc1_ch2_freq_mhz']:.3f} MHz")
            
            self.status_text.append("\n0x07：保留")
            if 'high_osc2_ch2_freq_mhz' in params:
                self.status_text.append(f"  频率: {params['high_osc2_ch2_freq_mhz']:.3f} MHz")
            
            self.status_text.append("\n0x08：高段一本振3通道参数")
            if 'high_osc1_ch3_freq_mhz' in params:
                self.status_text.append(f"  频率: {params['high_osc1_ch3_freq_mhz']:.3f} MHz")
            
            self.status_text.append("\n0x09：保留")
            if 'high_osc2_ch3_freq_mhz' in params:
                self.status_text.append(f"  频率: {params['high_osc2_ch3_freq_mhz']:.3f} MHz")
        else:
            # Single channel query
            channel_names = {
                0x01: "低段一本振1通道",
                0x02: "低段一本振2通道",
                0x03: "低段一本振工作模式",
                0x04: "高段一本振1通道",
                0x05: "保留",
                0x06: "高段一本振2通道",
                0x07: "保留",
                0x08: "高段一本振3通道",
                0x09: "保留"
            }
            
            channel_name = channel_names.get(params['channel_id'], f"0x{params['channel_id']:02X}")
            self.status_text.append(f"\n{channel_name}参数：")
            
            # Display frequency information
            if params.get('low_osc1_freq_khz', 0xFFFFFFFF) != 0xFFFFFFFF:
                self.status_text.append(f"  低段一本振频率: {params['low_osc1_freq_mhz']:.3f} MHz")
            if params.get('high_osc1_freq_khz', 0xFFFFFFFF) != 0xFFFFFFFF:
                self.status_text.append(f"  高段一本振频率: {params['high_osc1_freq_mhz']:.3f} MHz")
            if params.get('high_osc2_freq_khz', 0xFFFFFFFF) != 0xFFFFFFFF:
                self.status_text.append(f"  高段二本振频率: {params['high_osc2_freq_mhz']:.3f} MHz")
            
            # Display work mode
            if params['channel_id'] == 0x03:
                self.status_text.append(f"  工作模式: {params['work_mode_str']}")
            elif params['channel_id'] in [0x01, 0x02]:
                self.status_text.append(f"\n工作模式: {params['work_mode_str']}")
        
        self.status_text.append("")
        self.update_status_display()
        
    @pyqtSlot(str)
    def on_query_failed(self, error_msg):
        """Handle query failure"""
        self.log(error_msg, error=True)
        # Clear operation flag on failure
        self.operation_in_progress = False
    
    @pyqtSlot(bool, int)
    def on_params_set_complete(self, success, channel_id):
        """Handle parameter setting completion"""
        if success:
            self.log("参数设置成功")
            
            # Check if auto query is enabled
            if self.auto_query_checkbox.isChecked():
                # Since MCU doesn't respond to set command and first query fails,
                # we implement a retry mechanism
                self.log("等待MCU处理...")
                # Store channel_id for retry
                self.pending_query_channel = channel_id
                self.query_retry_count = 0
                # Start query with retry after shorter delay (200ms instead of 500ms)
                QTimer.singleShot(200, self.query_with_retry)
        else:
            self.log("参数设置失败", error=True)
    
    def query_with_retry(self):
        """Query channel parameters with retry mechanism"""
        if not hasattr(self, 'pending_query_channel'):
            return
            
        channel_id = self.pending_query_channel
        
        # Don't set operation_in_progress here since we're already in an operation
                
        self.query_worker = QueryWorker(self.protocol)
        self.query_worker.channel_params_ready.connect(self.on_channel_params_ready)
        self.query_worker.query_failed.connect(self.on_query_retry_failed)
        self.query_worker.log_message.connect(self.log)
        self.query_worker.status_message.connect(self.log_to_status)
        self.query_worker.finished.connect(self.on_query_worker_finished)
        
        try:
            self.log(f"查询通道 0x{channel_id:02X} 参数...")
            self.query_worker.start_query('channel_params', {'channel_id': channel_id})
        except Exception as e:
            self.log(f"启动查询失败: {str(e)}", error=True)
            # Clean up and clear flag
            if hasattr(self, 'pending_query_channel'):
                delattr(self, 'pending_query_channel')
            if hasattr(self, 'query_retry_count'):
                delattr(self, 'query_retry_count')
            self.operation_in_progress = False
    
    def on_query_retry_failed(self, error_msg):
        """Handle query failure with retry"""
        if hasattr(self, 'query_retry_count') and self.query_retry_count < 1:
            # First query failed as expected, retry once
            self.query_retry_count += 1
            self.log("首次查询失败，正在重试...")
            # Shorter retry delay (100ms instead of 300ms)
            QTimer.singleShot(100, self.query_with_retry)
        else:
            # Second query also failed, report error
            self.log(error_msg, error=True)
            # Clean up
            if hasattr(self, 'pending_query_channel'):
                delattr(self, 'pending_query_channel')
            if hasattr(self, 'query_retry_count'):
                delattr(self, 'query_retry_count')
    
    def on_query_worker_finished(self):
        """Safely handle query worker thread completion"""
        # Store reference to avoid race condition
        worker = getattr(self, 'query_worker', None)
        if worker is not None:
            # Disconnect all signals first to prevent crashes
            try:
                worker.blockSignals(True)
            except RuntimeError:
                pass  # Worker already deleted
            # Schedule for deletion
            worker.deleteLater()
            self.query_worker = None
        # Clear the operation flag
        self.operation_in_progress = False
        
    def send_oscillator_params(self, params):
        """Send oscillator parameters"""
        # Check if operation is already in progress
        if self.operation_in_progress:
            self.log("操作正在进行中，请稍后再试...")
            return
        
        # Set flag to prevent concurrent operations
        self.operation_in_progress = True
        
        try:
            self._do_send_oscillator_params(params)
        except Exception as e:
            self.log(f"发送参数时出错: {str(e)}", error=True)
            self.operation_in_progress = False
    
    def _stop_query_worker(self):
        """Safely stop and clean up query worker"""
        if hasattr(self, 'query_worker') and self.query_worker is not None:
            try:
                worker = self.query_worker  # Store reference to avoid race condition
                if worker and worker.isRunning():
                    self.log("停止当前操作...")
                    worker.terminate()
                    # Wait with timeout to avoid hanging
                    try:
                        if worker and not worker.wait(100):  # 100ms timeout
                            self.log("强制终止线程")
                    except RuntimeError:
                        # Thread already deleted, ignore
                        pass
            except Exception as e:
                self.log(f"停止线程时出错: {str(e)}")
            finally:
                self.query_worker = None
    
    def _do_send_oscillator_params(self, params):
        """Internal method to send oscillator parameters"""
        
        # Stop any ongoing query first
        self._stop_query_worker()
        
        # Create worker for setting params
        self.query_worker = QueryWorker(self.protocol)
        self.query_worker.params_set_complete.connect(self.on_params_set_complete)
        self.query_worker.log_message.connect(self.log)
        self.query_worker.status_message.connect(self.log_to_status)
        self.query_worker.finished.connect(self.on_query_worker_finished)
        
        try:
            self.query_worker.start_query('set_params', params)
        except Exception as e:
            self.log(f"启动参数设置失败: {str(e)}", error=True)
            self.operation_in_progress = False
            if self.query_worker:
                self.query_worker.deleteLater()
                self.query_worker = None
            
    def on_can_message_received(self, data):
        """Handle received CAN message"""
        # Log raw message
        hex_str = ' '.join(f'{b:02X}' for b in data)
        self.log(f"RX: {hex_str}", prefix="CAN")
        
        # Add to message queue
        self.message_queue.put(data)
        
    def on_complete_message_received(self, msg_type, data):
        """Handle complete multi-frame message"""
        self.log(f"收到完整的 {msg_type} 响应 ({len(data)} 字节)")
        
        # Add complete message to queue
        self.complete_message_queue.put((msg_type, data))
        
    def update_status_display(self):
        """Update the status display tab"""
        # Auto-scroll to bottom
        cursor = self.status_text.textCursor()
        cursor.movePosition(QTextCursor.End)
        self.status_text.setTextCursor(cursor)
        
        # Switch to status tab
        self.tab_widget.setCurrentIndex(1)
        
    def log_to_status(self, message):
        """Log message to status tab"""
        self.status_text.append(message)
        self.update_status_display()
        
    def log(self, message, error=False, prefix=""):
        """Add message to log"""
        # Handle when called as slot
        if isinstance(error, str) and not prefix:
            prefix = error
            error = False
            
        if prefix:
            message = f"[{prefix}] {message}"
            
        if error:
            message = f"错误: {message}"
            
        self.log_text.append(message)
    
    def clear_log(self):
        """Clear the log text"""
        self.log_text.clear()
        self.log("日志已清空")
    
    def clear_status(self):
        """Clear the status text"""
        self.status_text.clear()
        self.log_to_status("状态信息已清空")
        
    def closeEvent(self, event):
        """Handle window close event"""
        if self.connected:
            self.toggle_connection()
        event.accept()
    
    def update_product_info_display(self, info):
        """Update product info display from module info"""
        try:
            # Parse date from BCD format
            prod_date = info.get('production_date', 'YYYY-MM-DD')
            serial_num = info.get('serial_number', 'YYYY-BB-NNN')
            manufacturer = info.get('manufacturer_name', '未知')
            
            # Update current value label
            self.product_info_label.setText(
                f"当前值: 厂家{manufacturer}, 出厂{prod_date}, 序列号{serial_num}"
            )
            
            # Parse and update UI fields if format is correct
            if '-' in prod_date:
                parts = prod_date.split('-')
                if len(parts) == 3:
                    year, month, day = parts
                    self.production_date.setDate(QDate(int(year), int(month), int(day)))
            
            if '-' in serial_num:
                parts = serial_num.split('-')
                if len(parts) == 3:
                    year, batch, num = parts
                    self.serial_year_spin.setValue(int(year))
                    self.serial_batch_spin.setValue(int(batch))
                    self.serial_num_spin.setValue(int(num))
                    
            # Update manufacturer code
            if 'manufacturer_code' in info:
                self.manufacturer_spin.setValue(info['manufacturer_code'])
                
        except Exception as e:
            self.log(f"更新产品信息显示失败: {str(e)}", error=True)
    
    def read_product_info(self):
        """Read product information from device"""
        self.query_module_info()
    
    def write_product_info(self):
        """Write product information to device"""
        # Show warning dialog
        reply = QMessageBox.warning(
            self,
            "警告",
            "确定要写入产品信息吗？\n此操作将修改设备的产品信息！",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply != QMessageBox.Yes:
            return
        
        # Get values from UI
        manufacturer = self.manufacturer_spin.value()
        prod_date = self.production_date.date()
        serial_year = self.serial_year_spin.value()
        serial_batch = self.serial_batch_spin.value()
        serial_num = self.serial_num_spin.value()
        
        # Format the data for sending
        params = {
            'manufacturer_code': manufacturer,
            'production_year': prod_date.year(),
            'production_month': prod_date.month(),
            'production_day': prod_date.day(),
            'serial_year': serial_year,
            'serial_batch': serial_batch,
            'serial_num': serial_num
        }
        
        # Check if operation is already in progress
        if self.operation_in_progress:
            self.log("操作正在进行中，请稍后再试...")
            return
        
        self.operation_in_progress = True
        
        # Create worker for writing product info
        self.query_worker = QueryWorker(self.protocol)
        self.query_worker.params_set_complete.connect(self.on_product_info_written)
        self.query_worker.query_failed.connect(self.on_query_failed)
        self.query_worker.log_message.connect(self.log)
        self.query_worker.status_message.connect(self.log_to_status)
        self.query_worker.finished.connect(self.on_query_worker_finished)
        
        try:
            self.query_worker.start_query('write_product_info', params)
        except Exception as e:
            self.log(f"启动产品信息写入失败: {str(e)}", error=True)
            self.operation_in_progress = False
    
    @pyqtSlot(bool, int)
    def on_product_info_written(self, success, dummy):
        """Handle product info write completion"""
        if success:
            self.log("产品信息写入成功")
            # Read back to verify
            QTimer.singleShot(500, self.read_product_info)
        else:
            self.log("产品信息写入失败", error=True)
    
    def set_all_switches_on(self):
        """Set all switches to ON"""
        for checkbox in self.switch_checkboxes.values():
            checkbox.setChecked(True)
    
    def set_all_switches_off(self):
        """Set all switches to OFF"""
        for checkbox in self.switch_checkboxes.values():
            checkbox.setChecked(False)
    
    def apply_switch_settings(self):
        """Apply switch control settings"""
        if not self.connected:
            return
            
        # Ensure protocol has safe callback before direct calls
        # This prevents crashes if a QueryWorker was deleted
        self.protocol.set_status_log_callback(lambda msg: self.log_to_status(msg))
            
        # Collect switch states
        switch_states = {}
        for key, checkbox in self.switch_checkboxes.items():
            switch_states[key] = checkbox.isChecked()
        
        # Send switch control command
        success = self.protocol.set_switch_control(switch_states)
        
        if success:
            self.log("开关控制设置已发送")
            self.log_to_status("开关控制设置已应用")
        else:
            self.log("开关控制设置失败", error=True)


if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MainWindowV3()
    window.show()
    sys.exit(app.exec_())