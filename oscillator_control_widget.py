"""
Oscillator Control Widget for new protocol
Controls multiple oscillator channels
"""

from PyQt5.QtWidgets import (QGroupBox, QVBoxLayout, QHBoxLayout, QLabel, 
                             QDoubleSpinBox, QPushButton, QComboBox, QFrame,
                             QGridLayout, QCheckBox, QRadioButton, QButtonGroup)
from PyQt5.QtCore import pyqtSignal, Qt
from PyQt5.QtGui import QFont


class FixedFrequencyChannelControl(QFrame):
    """Fixed frequency point oscillator channel control"""
    
    frequency_changed = pyqtSignal(float)
    
    def __init__(self, channel_name, channel_id, freq_point1, freq_point2, parent=None):
        super().__init__(parent)
        self.channel_name = channel_name
        self.channel_id = channel_id
        self.freq_point1 = freq_point1
        self.freq_point2 = freq_point2
        self.init_ui()
        
    def init_ui(self):
        layout = QHBoxLayout()
        layout.setContentsMargins(5, 2, 5, 2)
        
        # Radio button for channel selection
        self.radio_button = QRadioButton()
        layout.addWidget(self.radio_button)
        
        # Channel name
        label = QLabel(self.channel_name)
        label.setMinimumWidth(80)
        layout.addWidget(label)
        
        # Frequency switching buttons and combo box
        freq_frame = QFrame()
        freq_layout = QHBoxLayout(freq_frame)
        freq_layout.setContentsMargins(0, 0, 0, 0)
        freq_layout.setSpacing(5)
        
        # Frequency point- button
        self.btn_freq_minus = QPushButton("频点-")
        self.btn_freq_minus.setMaximumWidth(50)
        self.btn_freq_minus.clicked.connect(self.switch_to_freq1)
        freq_layout.addWidget(self.btn_freq_minus)
        
        # Frequency point selection combo box
        self.freq_combo = QComboBox()
        self.freq_combo.addItem(f"{self.freq_point1:.0f} MHz", self.freq_point1)
        self.freq_combo.addItem(f"{self.freq_point2:.0f} MHz", self.freq_point2)
        self.freq_combo.setMinimumWidth(130)
        self.freq_combo.currentIndexChanged.connect(self.on_freq_changed)
        freq_layout.addWidget(self.freq_combo)
        
        # Frequency point+ button
        self.btn_freq_plus = QPushButton("频点+")
        self.btn_freq_plus.setMaximumWidth(50)
        self.btn_freq_plus.clicked.connect(self.switch_to_freq2)
        freq_layout.addWidget(self.btn_freq_plus)
        
        layout.addWidget(freq_frame)
        
        # Add stretch to fill remaining space
        layout.addStretch()
        
        self.setLayout(layout)
        
    def on_freq_changed(self):
        """Handle frequency selection change"""
        freq = self.freq_combo.currentData()
        if freq:
            self.frequency_changed.emit(freq)
            
    def get_frequency(self):
        """Get current selected frequency"""
        return self.freq_combo.currentData()
        
    def set_frequency(self, freq):
        """Set frequency (select closest point)"""
        # Select the closest frequency point
        if abs(freq - self.freq_point1) < abs(freq - self.freq_point2):
            self.freq_combo.setCurrentIndex(0)
        else:
            self.freq_combo.setCurrentIndex(1)
            
    def is_selected(self):
        """Check if this channel is selected"""
        return self.radio_button.isChecked()
    
    def on_frequency_changed(self, value):
        """Handle frequency change and emit signal immediately"""
        self.frequency_changed.emit(value)
    
    def switch_to_freq1(self):
        """Switch to frequency point 1"""
        self.freq_combo.setCurrentIndex(0)
    
    def switch_to_freq2(self):
        """Switch to frequency point 2"""
        self.freq_combo.setCurrentIndex(1)
        
    def set_lock_status(self, locked):
        """Set lock status (hidden in UI)"""
        pass  # Lock status display is hidden


class OscillatorChannelControl(QFrame):
    """Single oscillator channel control"""
    
    frequency_changed = pyqtSignal(float)
    
    def __init__(self, channel_name, channel_id, freq_min, freq_max, parent=None):
        super().__init__(parent)
        self.channel_name = channel_name
        self.channel_id = channel_id
        self.freq_min = freq_min
        self.freq_max = freq_max
        self.init_ui()
        
    def init_ui(self):
        layout = QHBoxLayout()
        layout.setContentsMargins(5, 2, 5, 2)
        
        # Radio button for channel selection
        self.radio_button = QRadioButton()
        layout.addWidget(self.radio_button)
        
        # Channel name
        label = QLabel(self.channel_name)
        label.setMinimumWidth(80)
        layout.addWidget(label)
        
        # Step buttons with frequency input in the middle
        step_frame = QFrame()
        step_layout = QHBoxLayout(step_frame)
        step_layout.setContentsMargins(0, 0, 0, 0)
        step_layout.setSpacing(5)
        
        # -10M button
        self.btn_minus_10m = QPushButton("-10M")
        self.btn_minus_10m.setMaximumWidth(40)
        self.btn_minus_10m.clicked.connect(lambda: self.adjust_frequency(-10))
        step_layout.addWidget(self.btn_minus_10m)
        
        # -1M button
        self.btn_minus_1m = QPushButton("-1M")
        self.btn_minus_1m.setMaximumWidth(35)
        self.btn_minus_1m.clicked.connect(lambda: self.adjust_frequency(-1))
        step_layout.addWidget(self.btn_minus_1m)
        
        # -1K button
        self.btn_minus_1k = QPushButton("-1K")
        self.btn_minus_1k.setMaximumWidth(35)
        self.btn_minus_1k.clicked.connect(lambda: self.adjust_frequency(-0.001))
        step_layout.addWidget(self.btn_minus_1k)
        
        # Frequency input in the middle
        self.freq_spinbox = QDoubleSpinBox()
        self.freq_spinbox.setRange(self.freq_min, self.freq_max)
        self.freq_spinbox.setDecimals(3)  # 1kHz precision
        self.freq_spinbox.setSuffix(" MHz")
        self.freq_spinbox.setSingleStep(0.001)  # 1kHz step
        self.freq_spinbox.setValue(self.freq_min)
        self.freq_spinbox.setMinimumWidth(130)
        # Connect to emit signal immediately on value change
        self.freq_spinbox.valueChanged.connect(self.on_frequency_changed)
        step_layout.addWidget(self.freq_spinbox)
        
        # +1K button
        self.btn_plus_1k = QPushButton("+1K")
        self.btn_plus_1k.setMaximumWidth(35)
        self.btn_plus_1k.clicked.connect(lambda: self.adjust_frequency(0.001))
        step_layout.addWidget(self.btn_plus_1k)
        
        # +1M button
        self.btn_plus_1m = QPushButton("+1M")
        self.btn_plus_1m.setMaximumWidth(35)
        self.btn_plus_1m.clicked.connect(lambda: self.adjust_frequency(1))
        step_layout.addWidget(self.btn_plus_1m)
        
        # +10M button
        self.btn_plus_10m = QPushButton("+10M")
        self.btn_plus_10m.setMaximumWidth(40)
        self.btn_plus_10m.clicked.connect(lambda: self.adjust_frequency(10))
        step_layout.addWidget(self.btn_plus_10m)
        
        layout.addWidget(step_frame)
        layout.addStretch()
        self.setLayout(layout)
        
    def set_frequency(self, freq_mhz):
        self.freq_spinbox.setValue(freq_mhz)
        
    def get_frequency(self):
        return self.freq_spinbox.value()
        
    def set_lock_status(self, locked):
        """Set lock status (hidden in UI)"""
        pass  # Lock status display is hidden
    
    def adjust_frequency(self, delta_mhz):
        """Adjust frequency by delta in MHz"""
        new_freq = self.freq_spinbox.value() + delta_mhz
        # Ensure within range
        new_freq = max(self.freq_min, min(self.freq_max, new_freq))
        self.freq_spinbox.setValue(new_freq)
    
    def on_frequency_changed(self, value):
        """Handle frequency change and emit signal immediately"""
        self.frequency_changed.emit(value)


class OscillatorControlWidget(QGroupBox):
    """Control widget for all oscillators"""
    
    # Signals
    send_params = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__("本振控制", parent)
        self.init_ui()
        
    def init_ui(self):
        # Create centered layout
        outer_layout = QVBoxLayout()
        
        # Create inner widget to center content
        inner_widget = QFrame()
        inner_widget.setMaximumWidth(600)  # Reduced width for compact layout
        main_layout = QVBoxLayout(inner_widget)
        
        # Low oscillator section
        low_label = QLabel("低段一本振 (30-3000 MHz)")
        low_label.setFont(QFont("Arial", 9, QFont.Bold))
        main_layout.addWidget(low_label)
        
        # Low oscillator group with radio buttons
        low_osc_frame = QFrame()
        low_osc_layout = QVBoxLayout(low_osc_frame)
        low_osc_layout.setContentsMargins(0, 0, 0, 0)
        
        # V0.5: 使用用户协议的频率范围
        self.low_osc1_ch1 = OscillatorChannelControl("通道1:", 0x01, 30, 3000)
        self.low_osc1_ch2 = OscillatorChannelControl("通道2:", 0x02, 30, 3000)
        
        # Create button group for low oscillator
        self.low_osc_group = QButtonGroup()
        self.low_osc_group.addButton(self.low_osc1_ch1.radio_button, 0x01)
        self.low_osc_group.addButton(self.low_osc1_ch2.radio_button, 0x02)
        self.low_osc1_ch1.radio_button.setChecked(True)  # Default selection
        
        low_osc_layout.addWidget(self.low_osc1_ch1)
        low_osc_layout.addWidget(self.low_osc1_ch2)
        
        # Work mode selection with immediate response
        mode_frame = QFrame()
        mode_layout = QHBoxLayout(mode_frame)
        mode_layout.setContentsMargins(0, 5, 0, 0)
        mode_layout.addWidget(QLabel("工作模式:"))
        
        mode_layout.addSpacing(10)  # Add space between label and combo box
        
        self.work_mode = QComboBox()
        self.work_mode.addItems([
            "低段一本振开关模式",
            "低段一本振功分模式", 
            "低功耗模式"
        ])
        self.work_mode.setCurrentIndex(1)  # Default to 功分模式
        self.work_mode.setMaximumWidth(200)
        # Connect for immediate response
        self.work_mode.currentIndexChanged.connect(lambda: self.send_channel_params(0x03))
        mode_layout.addWidget(self.work_mode)
        
        mode_layout.addStretch()
        low_osc_layout.addWidget(mode_frame)
        
        # Button frame for low oscillator
        low_btn_frame = QFrame()
        low_btn_layout = QHBoxLayout(low_btn_frame)
        low_btn_layout.setContentsMargins(10, 5, 10, 0)  # Add left/right margins
        
        # Add set button for low oscillator with consistent style
        self.btn_set_low = QPushButton("设置低段本振")
        self.btn_set_low.setToolTip("设置当前选中的低段一本振通道频率")
        self.btn_set_low.clicked.connect(self.send_low_osc_params)
        self.btn_set_low.setMinimumHeight(27)
        # Remove maximum width to allow expansion
        self.btn_set_low.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                font-weight: bold;
                padding: 5px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
            QPushButton:disabled {
                background-color: #CCCCCC;
            }
        """)
        low_btn_layout.addWidget(self.btn_set_low)
        
        low_osc_layout.addWidget(low_btn_frame)
        main_layout.addWidget(low_osc_frame)
        
        # High oscillator 1 section  
        main_layout.addSpacing(10)
        high1_label = QLabel("高段一本振 (2000-18000 MHz)")
        high1_label.setFont(QFont("Arial", 9, QFont.Bold))
        main_layout.addWidget(high1_label)
        
        # High oscillator 1 group with radio buttons
        high1_osc_frame = QFrame()
        high1_osc_layout = QVBoxLayout(high1_osc_frame)
        high1_osc_layout.setContentsMargins(0, 0, 0, 0)
        
        # V0.5: 使用用户协议的频率范围
        self.high_osc1_ch1 = OscillatorChannelControl("通道1:", 0x04, 2000, 18000)
        self.high_osc1_ch2 = OscillatorChannelControl("通道2:", 0x06, 2000, 18000)
        self.high_osc1_ch3 = OscillatorChannelControl("通道3:", 0x08, 2000, 18000)
        
        # Create button group for high oscillator 1
        self.high1_osc_group = QButtonGroup()
        self.high1_osc_group.addButton(self.high_osc1_ch1.radio_button, 0x04)
        self.high1_osc_group.addButton(self.high_osc1_ch2.radio_button, 0x06)
        self.high1_osc_group.addButton(self.high_osc1_ch3.radio_button, 0x08)
        self.high_osc1_ch1.radio_button.setChecked(True)  # Default selection
        
        high1_osc_layout.addWidget(self.high_osc1_ch1)
        high1_osc_layout.addWidget(self.high_osc1_ch2)
        high1_osc_layout.addWidget(self.high_osc1_ch3)
        
        # Button frame for high oscillator 1
        high1_btn_frame = QFrame()
        high1_btn_layout = QHBoxLayout(high1_btn_frame)
        high1_btn_layout.setContentsMargins(10, 5, 10, 0)  # Add left/right margins
        
        # Add set button for high oscillator 1 with consistent style
        self.btn_set_high1 = QPushButton("设置高段一本振")
        self.btn_set_high1.setToolTip("设置当前选中的高段一本振通道频率")
        self.btn_set_high1.clicked.connect(self.send_high1_osc_params)
        self.btn_set_high1.setMinimumHeight(27)
        # Remove maximum width to allow expansion
        self.btn_set_high1.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                font-weight: bold;
                padding: 5px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
            QPushButton:disabled {
                background-color: #CCCCCC;
            }
        """)
        high1_btn_layout.addWidget(self.btn_set_high1)
        
        high1_osc_layout.addWidget(high1_btn_frame)
        main_layout.addWidget(high1_osc_frame)
        
        # Note: High oscillator 2 removed in new protocol (0x05, 0x07, 0x09 are reserved)
        
        # Control buttons - full width layout
        main_layout.addSpacing(10)
        btn_frame = QFrame()
        btn_layout = QHBoxLayout(btn_frame)
        btn_layout.setContentsMargins(10, 0, 10, 0)  # Add left/right margins
        
        self.btn_send_all = QPushButton("设置所有通道（以通道1为准）")
        self.btn_send_all.setToolTip("将低段一本振通道1和高段一本振通道1的频率设置到所有通道")
        self.btn_send_all.clicked.connect(lambda: self.send_channel_params(0x00))
        self.btn_send_all.setMinimumHeight(27)
        self.btn_send_all.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
                padding: 5px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #45A049;
            }
            QPushButton:pressed {
                background-color: #2E7D32;
            }
            QPushButton:disabled {
                background-color: #CCCCCC;
            }
        """)
        btn_layout.addWidget(self.btn_send_all)
        
        main_layout.addWidget(btn_frame)
        
        # Center the inner widget
        outer_layout.addStretch()
        outer_layout.addWidget(inner_widget, 0, Qt.AlignHCenter)
        outer_layout.addStretch()
        
        self.setLayout(outer_layout)
        
        # Store all channels for easy access
        self.channels = {
            0x01: self.low_osc1_ch1,
            0x02: self.low_osc1_ch2,
            0x04: self.high_osc1_ch1,
            # 0x05: Reserved in new protocol
            0x06: self.high_osc1_ch2,
            # 0x07: Reserved in new protocol
            0x08: self.high_osc1_ch3,
            # 0x09: Reserved in new protocol
        }
        
        # Connect frequency change signals for immediate response
        self.low_osc1_ch1.frequency_changed.connect(lambda: self.on_frequency_changed(0x01))
        self.low_osc1_ch2.frequency_changed.connect(lambda: self.on_frequency_changed(0x02))
        self.high_osc1_ch1.frequency_changed.connect(lambda: self.on_frequency_changed(0x04))
        self.high_osc1_ch2.frequency_changed.connect(lambda: self.on_frequency_changed(0x06))
        self.high_osc1_ch3.frequency_changed.connect(lambda: self.on_frequency_changed(0x08))
        # High oscillator 2 signals removed (not in new protocol)
    
    def send_low_osc_params(self):
        """Send parameters for selected low oscillator channel"""
        channel_id = self.low_osc_group.checkedId()
        if channel_id != -1:
            self.send_channel_params(channel_id)
    
    def send_high1_osc_params(self):
        """Send parameters for selected high oscillator 1 channel"""
        channel_id = self.high1_osc_group.checkedId()
        if channel_id != -1:
            self.send_channel_params(channel_id)
    
    # send_high2_osc_params removed (high oscillator 2 not in new protocol)
        
    def send_channel_params(self, channel_id):
        """Send parameters for specified channel"""
        params = {
            'channel_id': channel_id,
            'work_mode': self.work_mode.currentIndex()
        }
        
        if channel_id == 0x00:  # All channels
            params['low_freq'] = self.low_osc1_ch1.get_frequency()
            params['high1_freq'] = self.high_osc1_ch1.get_frequency()
            # high2_freq removed (not in new protocol)
        elif channel_id in [0x01, 0x02]:  # Low oscillator
            params['low_freq'] = self.channels[channel_id].get_frequency()
        elif channel_id == 0x03:  # Work mode only
            pass  # work_mode already set
        elif channel_id in [0x04, 0x06, 0x08]:  # High oscillator 1
            params['high1_freq'] = self.channels[channel_id].get_frequency()
        # Note: 0x05, 0x07, 0x09 are reserved in new protocol
            
        self.send_params.emit(params)
        
    def update_lock_status(self, lock_status):
        """Update lock status indicators"""
        if 'low_osc1_ch1' in lock_status:
            self.low_osc1_ch1.set_lock_status(lock_status['low_osc1_ch1'])
        if 'low_osc1_ch2' in lock_status:
            self.low_osc1_ch2.set_lock_status(lock_status['low_osc1_ch2'])
        if 'high_osc1_ch1' in lock_status:
            self.high_osc1_ch1.set_lock_status(lock_status['high_osc1_ch1'])
        if 'high_osc1_ch2' in lock_status:
            self.high_osc1_ch2.set_lock_status(lock_status['high_osc1_ch2'])
        if 'high_osc1_ch3' in lock_status:
            self.high_osc1_ch3.set_lock_status(lock_status['high_osc1_ch3'])
        # High oscillator 2 lock status removed (not in new protocol)
    
    def on_frequency_changed(self, channel_id):
        """Handle frequency change for immediate response"""
        # Check if the channel is selected in its group
        if channel_id in [0x01, 0x02]:  # Low oscillator
            if self.low_osc_group.checkedId() == channel_id:
                self.send_channel_params(channel_id)
        elif channel_id in [0x04, 0x06, 0x08]:  # High oscillator 1
            if self.high1_osc_group.checkedId() == channel_id:
                self.send_channel_params(channel_id)
        # Note: 0x05, 0x07, 0x09 are reserved in new protocol