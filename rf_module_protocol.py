"""
RF Module CAN Protocol Implementation
Implements the CAN protocol for STM32F107-based RF modules
"""

import struct
import time
from datetime import datetime


class RFModuleProtocol:
    """RF Module protocol handler"""
    
    # Command IDs
    CMD_MODULE_INFO = [0xF0, 0x00]
    CMD_BIT_STATUS = [0xF0, 0x01]
    CMD_CHANNEL_PARAMS = [0xF0, 0x02]
    CMD_SET_PARAMS = [0xF0, 0x04]
    CMD_SWITCH_CONTROL = [0xF0, 0x0F]  # 开关控制调试指令
    
    # Channel IDs - New protocol channel definitions (5 channels total)
    CHANNEL_ALL = 0x00             # 所有通道同时操作
    CHANNEL_LOW_OSC1_CH1 = 0x01    # 低段一本振 通道1
    CHANNEL_LOW_OSC1_CH2 = 0x02    # 低段一本振 通道2
    CHANNEL_LOW_OSC1_MODE = 0x03   # 低段一本振 开关/功分切换（工作模式）
    CHANNEL_HIGH_OSC1_CH1 = 0x04   # 高段一本振 通道1
    CHANNEL_HIGH_OSC1_CH2 = 0x06   # 高段一本振 通道2
    CHANNEL_HIGH_OSC1_CH3 = 0x08   # 高段一本振 通道3
    # 0x05, 0x07, 0x09 are reserved
    
    # Channel groups for UI compatibility
    GROUP_ALL = 0x00
    GROUP_1_5 = 0x01  # Maps to low oscillator channels
    GROUP_6_10 = 0x02 # Maps to high oscillator channels
    
    # Work modes
    WORK_MODE_SWITCH = 0x00    # 低段一本振开关工作模式（共本振）
    WORK_MODE_DIVIDER = 0x01   # 低段一本振功分工作模式（独立本振）
    WORK_MODE_LOW_POWER = 0x02 # 低功耗工作模式
    
    # Manufacturer codes
    MANUFACTURER_CODES = {
        0x00: "十所",
        0x01: "振芯科技",
        0x02: "兴仁科技",
        0x03: "美数",
        0x04: "中电52所",
        0x05: "联邦微波"
    }
    
    # Bandwidth values
    BW_200KHZ = 0
    BW_5MHZ = 1
    BW_50MHZ = 2
    
    def __init__(self, can_interface):
        self.can = can_interface
        self.message_queue = None
        self.complete_message_queue = None
        self.log_callback = None
        self.status_log_callback = None
        
    def set_message_queue(self, queue):
        """Set the message queue for receiving messages"""
        self.message_queue = queue
        
    def set_complete_message_queue(self, queue):
        """Set the complete message queue for receiving assembled messages"""
        self.complete_message_queue = queue
        
    def set_log_callback(self, callback):
        """Set log callback for debug output"""
        self.log_callback = callback
        
    def set_status_log_callback(self, callback):
        """Set status log callback for human-readable status display"""
        self.status_log_callback = callback
        
    def _log(self, message):
        """Log message using callback or print"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(f"[Protocol] {message}")
        
    def query_module_info(self):
        """Query module product information
        
        Returns:
            dict: Module info or None if failed
        """
        # Record the query start time
        query_start = time.time()
        
        # Log and send query command
        hex_str = ' '.join(f'{b:02X}' for b in self.CMD_MODULE_INFO)
        self._log(f"TX: {hex_str}")
        
        if not self.can.send_can_message(self.CMD_MODULE_INFO):
            self._log("Failed to send module info query")
            return None
            
        # Wait for complete message
        if self.complete_message_queue:
            # Wait for module_info message
            start_time = time.time()
            timeout = 2.5  # Increased timeout to handle timing issues
            self._log(f"Waiting for module_info response (2 frames, 13 bytes)...")
            
            while (time.time() - start_time) < timeout:
                try:
                    msg = self.complete_message_queue.get(timeout=0.1)  # Shorter poll interval
                    
                    if msg:
                        msg_type, data = msg
                        self._log(f"Received message type: {msg_type}, length: {len(data)} bytes")
                        if msg_type == 'module_info':
                            # Parse response
                            return self.parse_module_info(data)
                        else:
                            self._log(f"Ignoring non-module_info message: {msg_type}")
                except Exception as e:
                    pass
                    
                # Small sleep to prevent busy waiting
                time.sleep(0.01)
            
            self._log(f"Timeout waiting for module_info response (waited {time.time() - start_time:.1f}s)")
            return None
        else:
            # Fallback to direct receive if no queue
            time.sleep(0.1)
            success, data = self.can.receive_multi_frame(2, timeout=2000)  # Changed to 2 frames (13 bytes)
            if not success or len(data) < 13:
                self._log(f"Failed to receive module info response, got {len(data)} bytes")
                return None
                
            if data[0] != 0xF0 or data[1] != 0x00:
                self._log("Invalid module info response header")
                return None
                
            return self.parse_module_info(data)
        
    def parse_module_info(self, data):
        """Parse module info response data
        
        Args:
            data: 13 bytes of response data (new protocol)
            
        Returns:
            dict: Parsed module information
        """
        info = {}
        
        # Manufacturer code
        info['manufacturer'] = data[2]
        info['manufacturer_name'] = self.MANUFACTURER_CODES.get(data[2], f"Unknown({data[2]})")
        
        # Production date (BCD format: YYYY-MM-DD)
        # BCD is not affected by endianness, it's in natural order
        year_bcd = (data[3] << 8) | data[4]  # BYTE3 has high digits, BYTE4 has low digits
        month_bcd = data[5]
        day_bcd = data[6]
        info['production_date'] = self.decode_bcd_date(year_bcd, month_bcd, day_bcd)
        
        # Serial number (BCD format: YYYY-BB-NNN)
        # BCD is not affected by endianness, it's in natural order
        serial_year_bcd = (data[7] << 8) | data[8]  # BYTE7 has high digits, BYTE8 has low digits
        batch_bcd = data[9]
        # Note: data[10] bits 7-4 are reserved
        serial_num_bcd = ((data[10] & 0x0F) << 8) | data[11]  # High nibble from BYTE10, full BYTE11
        info['serial_number'] = self.decode_bcd_serial(serial_year_bcd, batch_bcd, serial_num_bcd)
        
        # MARK address
        info['mark_address'] = data[12]
        
        # No frequency ranges in new protocol's module info
        # Set default ranges for compatibility
        info['freq_ranges'] = {
            'low_osc1': {
                'low': 30000,      # 30 MHz
                'high': 3000000,   # 3000 MHz
                'low_mhz': 30.0,
                'high_mhz': 3000.0
            },
            'high_osc1': {
                'low': 2000000,    # 2000 MHz
                'high': 18000000,  # 18000 MHz
                'low_mhz': 2000.0,
                'high_mhz': 18000.0
            }
        }
        
        # Keep old format for compatibility
        info['freq_range'] = info['freq_ranges']['low_osc1']
        
        return info
        
    def query_bit_status(self):
        """Query module BIT (Built-In Test) status
        
        Returns:
            dict: BIT status or None if failed
        """
        # Record the query start time
        query_start = time.time()
            
        # Log and send query command
        hex_str = ' '.join(f'{b:02X}' for b in self.CMD_BIT_STATUS)
        self._log(f"TX: {hex_str}")
        
        if not self.can.send_can_message(self.CMD_BIT_STATUS):
            self._log("Failed to send BIT status query")
            return None
            
        # Wait for complete message
        if self.complete_message_queue:
            # Wait for bit_status message
            start_time = time.time()
            timeout = 2.5  # Increased timeout to handle timing issues
            
            while (time.time() - start_time) < timeout:
                try:
                    msg = self.complete_message_queue.get(timeout=0.1)  # Shorter poll interval
                    if msg:
                        msg_type, data = msg
                        if msg_type == 'bit_status':
                            # Parse response
                            return self.parse_bit_status(data)
                except Exception as e:
                    self._log(f"DEBUG: Queue get error: {e}")
                    
                # Small sleep to prevent busy waiting
                time.sleep(0.01)
            
            return None
        else:
            # Fallback to direct receive if no queue
            time.sleep(0.1)
            success, data = self.can.receive_multi_frame(2, timeout=2000)
            if not success or len(data) < 14:
                self._log(f"Failed to receive BIT status response, got {len(data)} bytes")
                return None
                
            if data[0] != 0xF0 or data[1] != 0x01:
                self._log("Invalid BIT status response header")
                return None
                
            return self.parse_bit_status(data)
        
    def parse_bit_status(self, data):
        """Parse BIT status response data
        
        Args:
            data: 14 bytes of response data
            
        Returns:
            dict: Parsed BIT status
        """
        status = {}
        
        # Temperature (signed byte, -55 to 75°C according to new protocol)
        temp = data[2]
        if temp > 127:
            temp = temp - 256  # Convert to signed
        status['temperature'] = temp
        
        # Channel voltages (0.1V precision, 0xFF = invalid)
        # Channels 1-8: +5.5V detection voltage
        status['channel_voltages'] = []
        for i in range(8):
            voltage_raw = data[3 + i]
            if voltage_raw == 0xFF:
                voltage = None  # Invalid
            else:
                voltage = voltage_raw * 0.1
            status['channel_voltages'].append(voltage)
        
        # -5V detection voltage
        voltage_raw = data[11]
        if voltage_raw == 0xFF:
            status['negative_5v'] = None
        else:
            # Signed value for negative voltage
            if voltage_raw > 127:
                voltage_raw = voltage_raw - 256
            status['negative_5v'] = voltage_raw * 0.1
        
        # Lock status bits (from BYTE12-13) - New protocol definition
        # Little-endian: low byte first
        lock_status_word = data[13] << 8 | data[12]
        
        status['lock_status'] = {
            'low_osc1_ch1': (lock_status_word & 0x0001) != 0,  # BIT0 - 低段一本振1通道锁定指示
            'low_osc1_ch2': (lock_status_word & 0x0002) != 0,  # BIT1 - 低段一本振2通道锁定指示
            'low_osc2_locked': (lock_status_word & 0x0004) != 0,  # BIT2 - 低段二本振通道锁定指示
            'low_osc2_ref_normal': (lock_status_word & 0x0008) != 0,  # BIT3 - 低段二本振参考输入信号状态检测
            'high_osc1_ch1': (lock_status_word & 0x0010) != 0,  # BIT4 - 高段一本振1通道锁定指示
            'high_osc1_ch2': (lock_status_word & 0x0040) != 0,  # BIT6 - 高段一本振2通道锁定指示
            'high_osc1_ch3': (lock_status_word & 0x0100) != 0,  # BIT8 - 高段一本振3通道锁定指示
            # BIT5, BIT7, BIT9 are for 高段二本振 (removed in new protocol)
            # BIT10-15 are reserved
        }
            
        return status
        
    def query_channel_params(self, channel_id=CHANNEL_ALL):
        """Query channel parameters
        
        Args:
            channel_id: Channel ID (0x00-0x09)
            
        Returns:
            dict: Channel parameters or None if failed
        """
        # Record the query start time
        query_start = time.time()
            
        # Clear receive buffer before sending query
        self.can.clear_receive_buffer()
        
        # Also clear the message queue
        if self.complete_message_queue:
            # Clear any pending messages
            while True:
                try:
                    self.complete_message_queue.get_nowait()
                except:
                    break
        
        # Log and send query command
        cmd = self.CMD_CHANNEL_PARAMS + [channel_id]
        hex_str = ' '.join(f'{b:02X}' for b in cmd)
        self._log(f"TX: {hex_str}")
        
        # Add small delay to ensure previous operation completed
        time.sleep(0.050)  # 50ms delay
        
        if not self.can.send_can_message(cmd):
            self._log("Failed to send channel params query")
            return None
            
        # Wait for complete message
        if self.complete_message_queue:
            # Wait for channel_params message
            start_time = time.time()
            timeout = 2.5  # Increased timeout to handle timing issues
            
            while (time.time() - start_time) < timeout:
                try:
                    msg = self.complete_message_queue.get(timeout=0.1)  # Shorter poll interval
                    if msg:
                        msg_type, data = msg
                        if msg_type == 'channel_params':
                            # Parse response
                            return self.parse_channel_params_v3(data)
                except Exception as e:
                    self._log(f"DEBUG: Queue get error: {e}")
                    
                # Small sleep to prevent busy waiting
                time.sleep(0.01)
            
            return None
        else:
            # Fallback to direct receive if no queue
            time.sleep(0.1)
            # Determine expected frames based on channel_id
            if channel_id == self.CHANNEL_ALL:
                expected_frames = 4  # 25 bytes for all channels (3*8 + 1)
            else:
                expected_frames = 2  # Single channel query not used in new protocol
            
            success, data = self.can.receive_multi_frame(expected_frames, timeout=2000)
            if not success:
                self._log(f"Failed to receive channel params response")
                return None
                
            if data[0] != 0xF0 or data[1] != 0x02:
                self._log("Invalid channel params response header")
                return None
                
            return self.parse_channel_params_v3(data)
        
    def parse_channel_params_v3(self, data):
        """Parse channel parameters response (new protocol)
        
        Args:
            data: Response data bytes (25 bytes for all channels)
            
        Returns:
            dict: Parsed parameters
        """
        params = {}
        
        # Channel ID
        channel_id = data[2]
        params['channel_id'] = channel_id
        
        # Low segment reference input signal status (BYTE3)
        params['low_osc2_ref_status'] = data[3]  # 0=异常, 1=正常
        
        # Parse frequency data for all channels query (25 bytes total)
        if channel_id == self.CHANNEL_ALL and len(data) >= 25:
            # Low oscillator 1 channel 1 frequency (BYTE4-7)
            # Little-endian: use '<I' instead of '>I'
            params['low_osc1_freq_khz'] = struct.unpack('<I', bytes(data[4:8]))[0]
            params['low_osc1_freq_mhz'] = params['low_osc1_freq_khz'] / 1000.0
            
            # Low oscillator 1 channel 2 frequency (BYTE8-11)
            # Little-endian: use '<I' instead of '>I'
            params['low_osc1_ch2_freq_khz'] = struct.unpack('<I', bytes(data[8:12]))[0]
            params['low_osc1_ch2_freq_mhz'] = params['low_osc1_ch2_freq_khz'] / 1000.0
            
            # High oscillator 1 channel 1 frequency (BYTE12-15)
            # Little-endian: use '<I' instead of '>I'
            params['high_osc1_freq_khz'] = struct.unpack('<I', bytes(data[12:16]))[0]
            params['high_osc1_freq_mhz'] = params['high_osc1_freq_khz'] / 1000.0
            
            # High oscillator 1 channel 2 frequency (BYTE16-19)
            # Little-endian: use '<I' instead of '>I'
            params['high_osc1_ch2_freq_khz'] = struct.unpack('<I', bytes(data[16:20]))[0]
            params['high_osc1_ch2_freq_mhz'] = params['high_osc1_ch2_freq_khz'] / 1000.0
            
            # High oscillator 1 channel 3 frequency (BYTE20-23)
            # Little-endian: use '<I' instead of '>I'
            params['high_osc1_ch3_freq_khz'] = struct.unpack('<I', bytes(data[20:24]))[0]
            params['high_osc1_ch3_freq_mhz'] = params['high_osc1_ch3_freq_khz'] / 1000.0
            
            # Work mode (BYTE24)
            work_mode = data[24] & 0x03
            params['work_mode'] = work_mode
            params['work_mode_str'] = {
                0: "低段一本振开关工作模式",
                1: "低段一本振功分工作模式",
                2: "低功耗工作模式"
            }.get(work_mode, "未知")
        else:
            # Single channel query - simplified parsing
            params['low_osc1_freq_khz'] = 0xFFFFFFFF  # Invalid
            params['high_osc1_freq_khz'] = 0xFFFFFFFF  # Invalid
            
        return params
    
    def parse_channel_params(self, data, channel_group):
        """Parse channel parameters response (compatibility wrapper)
        
        Args:
            data: Response data bytes
            channel_group: The requested channel group
            
        Returns:
            dict: Parsed parameters
        """
        # For backward compatibility with V0.2
        params = self.parse_channel_params_v3(data)
        
        # Convert to old format
        if channel_group == self.GROUP_ALL:
            # Simulate old group format
            result = {
                'group_1_5': {
                    'freq_mhz': params.get('low_osc1_freq_mhz', 100.0),
                    'freq_khz': params.get('low_osc1_freq_khz', 100000),
                    'rf_att_db': 0,  # Not in new protocol
                    'if_att_db': 0,  # Not in new protocol
                    'bandwidth': 1,  # Default to 5MHz
                    'bandwidth_str': "5 MHz",
                    'power_on': params.get('work_mode', 0) != 2  # Not low power
                },
                'group_6_10': {
                    'freq_mhz': params.get('high_osc1_freq_mhz', 2000.0),
                    'freq_khz': params.get('high_osc1_freq_khz', 2000000),
                    'rf_att_db': 0,  # Not in new protocol
                    'if_att_db': 0,  # Not in new protocol
                    'bandwidth': 1,  # Default to 5MHz
                    'bandwidth_str': "5 MHz",
                    'power_on': params.get('work_mode', 0) != 2  # Not low power
                }
            }
            return result
        else:
            # Single group
            group_name = 'group_1_5' if channel_group == self.GROUP_1_5 else 'group_6_10'
            freq_key = 'low_osc1_freq_mhz' if channel_group == self.GROUP_1_5 else 'high_osc1_freq_mhz'
            freq_khz_key = 'low_osc1_freq_khz' if channel_group == self.GROUP_1_5 else 'high_osc1_freq_khz'
            
            return {
                group_name: {
                    'freq_mhz': params.get(freq_key, 100.0),
                    'freq_khz': params.get(freq_khz_key, 100000),
                    'rf_att_db': 0,  # Not in new protocol
                    'if_att_db': 0,  # Not in new protocol
                    'bandwidth': 1,  # Default to 5MHz
                    'bandwidth_str': "5 MHz",
                    'power_on': params.get('work_mode', 0) != 2  # Not low power
                }
            }
        
    def parse_single_group(self, data):
        """Parse single channel group parameters
        
        Args:
            data: 8 bytes of group data
            
        Returns:
            dict: Parsed group parameters
        """
        group = {}
        
        # RF attenuation (0-7 → 0-35 dB)
        group['rf_att_val'] = data[1]
        group['rf_att_db'] = data[1] * 5
        
        # IF attenuation (0-30 dB)
        group['if_att_db'] = data[2]
        
        # Frequency (kHz, little-endian)
        freq_khz = struct.unpack('<I', bytes(data[3:7]))[0]
        group['freq_khz'] = freq_khz
        group['freq_mhz'] = freq_khz / 1000.0
        
        # Control byte: bandwidth and work mode
        control = data[7]
        group['bandwidth'] = (control >> 1) & 0x03
        group['work_mode'] = control & 0x01
        group['power_on'] = (control & 0x01) == 0  # 0=ON, 1=OFF
        
        # Bandwidth string
        bw_map = {0: "200 kHz", 1: "5 MHz", 2: "50 MHz"}
        group['bandwidth_str'] = bw_map.get(group['bandwidth'], "Unknown")
        
        return group
        
    def set_channel_params_v3(self, channel_id, low_freq_mhz=None, high1_freq_mhz=None, work_mode=None):
        """Set channel parameters (New protocol - 12 bytes)
        
        Args:
            channel_id: Channel ID (0x00-0x09, but 0x05/0x07/0x09 are reserved)
            low_freq_mhz: Low oscillator 1 frequency in MHz (30-3000)
            high1_freq_mhz: High oscillator 1 frequency in MHz (2000-18000)
            work_mode: Work mode (0=switch, 1=divider, 2=low power)
            
        Returns:
            bool: True if successful
        """
        # Build the command based on channel_id
        if channel_id == self.CHANNEL_LOW_OSC1_MODE:
            # Special case: only set work mode
            if work_mode is None:
                self._log("Work mode required for channel 0x03")
                return False
        
        # Convert frequencies to kHz
        low_freq_khz = int(low_freq_mhz * 1000) if low_freq_mhz else 0
        high1_freq_khz = int(high1_freq_mhz * 1000) if high1_freq_mhz else 0
        
        # Default work mode if not specified
        if work_mode is None:
            work_mode = self.WORK_MODE_DIVIDER
        
        # Build 12-byte message (2 frames: 8 + 4) - New protocol format
        # Frame 1: Command + channel + work mode + low freq (little-endian)
        frame1 = [
            0xF0, 0x04, channel_id,
            (work_mode & 0x03),  # BYTE3 bits 1-0 are work mode
            low_freq_khz & 0xFF,          # Little-endian: low byte first
            (low_freq_khz >> 8) & 0xFF,
            (low_freq_khz >> 16) & 0xFF,
            (low_freq_khz >> 24) & 0xFF
        ]
        
        # Frame 2: High oscillator 1 frequency (4 bytes only, little-endian)
        frame2 = [
            high1_freq_khz & 0xFF,          # Little-endian: low byte first
            (high1_freq_khz >> 8) & 0xFF,
            (high1_freq_khz >> 16) & 0xFF,
            (high1_freq_khz >> 24) & 0xFF
        ]
        
        # Log the operation
        if self.status_log_callback:
            channel_names = {
                0x00: "所有通道",
                0x01: "低段一本振 通道1",
                0x02: "低段一本振 通道2",
                0x03: "低段一本振 工作模式",
                0x04: "高段一本振 通道1",
                0x05: "保留",
                0x06: "高段一本振 通道2",
                0x07: "保留",
                0x08: "高段一本振 通道3",
                0x09: "保留"
            }
            work_mode_names = {
                0: "低段一本振开关工作模式",
                1: "低段一本振功分工作模式",
                2: "低功耗工作模式"
            }
            
            status_msg = f"设置参数 - {channel_names.get(channel_id, '未知')}\n"
            if low_freq_mhz:
                status_msg += f"  低段频率: {low_freq_mhz:.3f} MHz\n"
            if high1_freq_mhz:
                status_msg += f"  高段一频率: {high1_freq_mhz:.3f} MHz\n"
            status_msg += f"  工作模式: {work_mode_names.get(work_mode, '未知')}"
            self.status_log_callback(status_msg)
        
        # Log raw data
        hex_str1 = ' '.join(f'{b:02X}' for b in frame1)
        hex_str2 = ' '.join(f'{b:02X}' for b in frame2)
        self._log(f"TX Frame 1: {hex_str1}")
        self._log(f"TX Frame 2: {hex_str2}")
        
        # Clear receive buffer
        self.can.clear_receive_buffer()
        
        # Send frames
        if not self.can.send_can_message(frame1):
            self._log("Failed to send frame 1")
            return False
            
        time.sleep(0.008)  # 8ms delay
        
        if not self.can.send_can_message(frame2):
            self._log("Failed to send frame 2")
            return False
            
        time.sleep(0.008)  # 8ms delay
        
        # Send empty frame to indicate end of multi-frame message
        # This is required when the last data frame has 8 bytes
        empty_frame = []
        hex_str_empty = '(empty frame)'
        self._log(f"TX Frame 3: {hex_str_empty}")
        
        if not self.can.send_can_message(empty_frame):
            self._log("Failed to send empty frame")
            return False
            
        return True
    
    def write_product_info(self, params):
        """Write product information to device
        
        Args:
            params: Dictionary containing:
                - manufacturer_code: int (0-255)
                - production_year: int (e.g. 2025)
                - production_month: int (1-12)
                - production_day: int (1-31)
                - serial_year: int (e.g. 2025)
                - serial_batch: int (1-99)
                - serial_num: int (1-999)
                
        Returns:
            bool: True if successful
        """
        # Convert to BCD format
        def to_bcd(val):
            """Convert integer to BCD"""
            return ((val // 10) << 4) | (val % 10)
        
        # Build command
        cmd_data = [
            0xF0, 0x0E,  # Command ID
            params['manufacturer_code'],
            to_bcd(params['production_year'] // 100),  # Year high
            to_bcd(params['production_year'] % 100),  # Year low
            to_bcd(params['production_month']),
            to_bcd(params['production_day']),
            to_bcd(params['serial_year'] // 100),  # Serial year high
            to_bcd(params['serial_year'] % 100),   # Serial year low
            to_bcd(params['serial_batch']),
            to_bcd(params['serial_num'] // 100),    # Serial num hundreds
            to_bcd(params['serial_num'] % 100)      # Serial num tens/ones
        ]
        
        # Log the operation
        if self.status_log_callback:
            manufacturer_name = self.MANUFACTURER_CODES.get(params['manufacturer_code'], "未知")
            self.status_log_callback(
                f"写入产品信息:\n"
                f"  厂家: {manufacturer_name} (代号{params['manufacturer_code']})\n"
                f"  出厂日期: {params['production_year']}-{params['production_month']:02d}-{params['production_day']:02d}\n"
                f"  序列号: {params['serial_year']}-{params['serial_batch']:02d}-{params['serial_num']:03d}"
            )
        
        # Send in 2 frames
        frame1 = cmd_data[:8]
        frame2 = cmd_data[8:] + [0] * (8 - len(cmd_data[8:]))  # Pad to 8 bytes
        
        hex_str1 = ' '.join(f'{b:02X}' for b in frame1)
        hex_str2 = ' '.join(f'{b:02X}' for b in frame2)
        self._log(f"TX Frame 1: {hex_str1}")
        self._log(f"TX Frame 2: {hex_str2}")
        
        # Clear receive buffer
        self.can.clear_receive_buffer()
        
        # Send frames
        if not self.can.send_can_message(frame1):
            self._log("Failed to send frame 1")
            return False
            
        time.sleep(0.008)
        
        if not self.can.send_can_message(frame2):
            self._log("Failed to send frame 2")
            return False
            
        time.sleep(0.008)
        
        # Send empty frame to indicate end
        empty_frame = []
        self._log(f"TX Frame 3: (empty frame)")
        
        if not self.can.send_can_message(empty_frame):
            self._log("Failed to send empty frame")
            return False
        
        # Wait for response
        timeout = 1.0  # 1 second timeout
        start_time = time.time()
        
        while (time.time() - start_time) < timeout:
            if self.message_queue:
                try:
                    msg = self.message_queue.get(timeout=0.1)
                    if len(msg) >= 3 and msg[0] == 0xF0 and msg[1] == 0x0E:
                        if msg[2] == 0x00:
                            self._log("Product info write confirmed")
                            return True
                        else:
                            self._log(f"Product info write failed, error code: {msg[2]}")
                            return False
                except:
                    pass
            time.sleep(0.01)
        
        self._log("Product info write timeout")
        return False
    
    def set_channel_params(self, channel_group, freq_mhz, rf_att_db, 
                          if_att_db, bandwidth_idx, power_on):
        """Set channel parameters (compatibility wrapper for old UI)
        
        Args:
            channel_group: GROUP_ALL, GROUP_1_5, or GROUP_6_10
            freq_mhz: Frequency in MHz
            rf_att_db: RF attenuation in dB (ignored in new protocol)
            if_att_db: IF attenuation in dB (ignored in new protocol)
            bandwidth_idx: Bandwidth index (ignored in new protocol)
            power_on: True for power ON, False for OFF
            
        Returns:
            bool: True if successful
        """
        # Map old groups to new channel IDs
        # For compatibility, we'll set the first channel of each group
        if channel_group == self.GROUP_1_5:
            # Set low oscillator channel 1
            channel_id = self.CHANNEL_LOW_OSC1_CH1
            work_mode = self.WORK_MODE_DIVIDER if power_on else self.WORK_MODE_LOW_POWER
            return self.set_channel_params_v3(channel_id, low_freq_mhz=freq_mhz, work_mode=work_mode)
        elif channel_group == self.GROUP_6_10:
            # Set high oscillator 1 channel 1
            channel_id = self.CHANNEL_HIGH_OSC1_CH1
            work_mode = self.WORK_MODE_DIVIDER if power_on else self.WORK_MODE_LOW_POWER
            return self.set_channel_params_v3(channel_id, high1_freq_mhz=freq_mhz, work_mode=work_mode)
        else:
            # Set all channels
            work_mode = self.WORK_MODE_DIVIDER if power_on else self.WORK_MODE_LOW_POWER
            # For all channels, set reasonable defaults
            return self.set_channel_params_v3(self.CHANNEL_ALL, 
                                             low_freq_mhz=freq_mhz if freq_mhz <= 3000 else 100,
                                             high1_freq_mhz=freq_mhz if freq_mhz > 3000 else 5000,
                                             work_mode=work_mode)
        
    def decode_bcd_date(self, year_bcd, month_bcd, day_bcd):
        """Decode BCD date format
        
        Args:
            year_bcd: Year in BCD (e.g., 0x2024)
            month_bcd: Month in BCD (e.g., 0x12)
            day_bcd: Day in BCD (e.g., 0x31)
            
        Returns:
            str: Formatted date string
        """
        year = ((year_bcd >> 12) & 0xF) * 1000 + \
               ((year_bcd >> 8) & 0xF) * 100 + \
               ((year_bcd >> 4) & 0xF) * 10 + \
               (year_bcd & 0xF)
               
        month = ((month_bcd >> 4) & 0xF) * 10 + (month_bcd & 0xF)
        day = ((day_bcd >> 4) & 0xF) * 10 + (day_bcd & 0xF)
        
        return f"{year:04d}-{month:02d}-{day:02d}"
        
    def decode_bcd_serial(self, year_bcd, batch_bcd, serial_bcd):
        """Decode BCD serial number format
        
        Args:
            year_bcd: Year in BCD
            batch_bcd: Batch number in BCD
            serial_bcd: Serial number in BCD
            
        Returns:
            str: Formatted serial number string
        """
        year = ((year_bcd >> 12) & 0xF) * 1000 + \
               ((year_bcd >> 8) & 0xF) * 100 + \
               ((year_bcd >> 4) & 0xF) * 10 + \
               (year_bcd & 0xF)
               
        batch = ((batch_bcd >> 4) & 0xF) * 10 + (batch_bcd & 0xF)
        
        serial = ((serial_bcd >> 12) & 0xF) * 1000 + \
                 ((serial_bcd >> 8) & 0xF) * 100 + \
                 ((serial_bcd >> 4) & 0xF) * 10 + \
                 (serial_bcd & 0xF)
        
        return f"{year:04d}-{batch:02d}-{serial:03d}"
    
    def set_product_info(self, manufacturer, prod_year_bcd, prod_month_bcd, 
                        prod_day_bcd, serial_year_bcd, serial_batch_bcd, 
                        serial_num_bcd):
        """Set product information (debug function)
        
        Args:
            manufacturer: Manufacturer code (0-255)
            prod_year_bcd: Production year in BCD
            prod_month_bcd: Production month in BCD
            prod_day_bcd: Production day in BCD
            serial_year_bcd: Serial year in BCD
            serial_batch_bcd: Serial batch in BCD
            serial_num_bcd: Serial number in BCD
            
        Returns:
            bool: True if successful
        """
        # Log the human-readable info
        if self.status_log_callback:
            prod_year = self.bcd_to_int(prod_year_bcd)
            prod_month = self.bcd_to_int(prod_month_bcd)
            prod_day = self.bcd_to_int(prod_day_bcd)
            serial_year = self.bcd_to_int(serial_year_bcd)
            serial_batch = self.bcd_to_int(serial_batch_bcd)
            serial_num = self.bcd_to_int(serial_num_bcd)
            
            # 厂家代号映射
            manufacturer_name = self.MANUFACTURER_CODES.get(manufacturer, f"Unknown({manufacturer})")
            
            status_msg = f"写入产品信息\n"
            status_msg += f"  厂家代号: {manufacturer_name} ({manufacturer})\n"
            status_msg += f"  出厂日期: {prod_year:04d}-{prod_month:02d}-{prod_day:02d}\n"
            status_msg += f"  序列号: {serial_year:04d}-{serial_batch:02d}-{serial_num:03d}"
            self.status_log_callback(status_msg)
        
        # Build command
        # Multi-frame message (12 bytes total)
        # Frame 1: Command + manufacturer + production date (BCD in natural order)
        frame1 = [
            0xF0, 0x0E,  # Command
            manufacturer,
            (prod_year_bcd >> 8) & 0xFF,  # Year high byte (thousands and hundreds)
            prod_year_bcd & 0xFF,          # Year low byte (tens and ones)
            prod_month_bcd,
            prod_day_bcd,
            (serial_year_bcd >> 8) & 0xFF  # Serial year high (thousands and hundreds)
        ]
        
        # Frame 2: Serial info (BCD in natural order)
        frame2 = [
            serial_year_bcd & 0xFF,         # Serial year low (tens and ones)
            serial_batch_bcd,
            (serial_num_bcd >> 8) & 0xFF,   # Serial num high (hundreds)
            serial_num_bcd & 0xFF           # Serial num low (tens and ones)
        ]
        
        # Log the raw data
        hex_str1 = ' '.join(f'{b:02X}' for b in frame1)
        hex_str2 = ' '.join(f'{b:02X}' for b in frame2)
        self._log(f"TX Frame 1: {hex_str1}")
        self._log(f"TX Frame 2: {hex_str2}")
        
        # Clear receive buffer
        self.can.clear_receive_buffer()
        
        # Send frames
        if not self.can.send_can_message(frame1):
            self._log("Failed to send frame 1")
            return False
            
        time.sleep(0.008)  # 8ms delay
        
        if not self.can.send_can_message(frame2):
            self._log("Failed to send frame 2")
            return False
            
        # Wait for response
        time.sleep(0.1)
        success, data = self.can.receive_can_message(timeout=1000)
        
        if success and len(data) >= 3:
            if data[0] == 0xF0 and data[1] == 0x0E and data[2] == 0x00:
                self._log("Product info write confirmed")
                return True
            else:
                self._log(f"Unexpected response: {' '.join(f'{b:02X}' for b in data)}")
                return False
        else:
            self._log("No response received")
            return False
            
    def bcd_to_int(self, bcd_value):
        """Convert BCD to integer"""
        if bcd_value > 0xFF:
            # 4-digit BCD (year)
            return ((bcd_value >> 12) & 0xF) * 1000 + \
                   ((bcd_value >> 8) & 0xF) * 100 + \
                   ((bcd_value >> 4) & 0xF) * 10 + \
                   (bcd_value & 0xF)
        else:
            # 2-digit BCD
            return ((bcd_value >> 4) & 0xF) * 10 + (bcd_value & 0xF)
    
    def set_switch_control(self, switch_states):
        """Set oscillator switch control (debug function)
        
        Args:
            switch_states: dict with keys:
                - llo1_1: bool, Low oscillator 1 channel 1 power
                - llo1_2: bool, Low oscillator 1 channel 2 power
                - llo2_1: bool, Low oscillator 2 channel 1 power
                - llo2_2: bool, Low oscillator 2 channel 2 power
                - hlo1_1: bool, High oscillator 1 channel 1 power
                - hlo1_2: bool, High oscillator 1 channel 2 power
                - hlo1_3: bool, High oscillator 1 channel 3 power
                - hlo2_1: bool, High oscillator 2 channel 1 power
                - hlo2_2: bool, High oscillator 2 channel 2 power
                - hlo2_3: bool, High oscillator 2 channel 3 power
                
        Returns:
            bool: True if successful
        """
        # Build 16-bit switch control value
        switch_control = 0
        
        # Map switch states to bits
        if switch_states.get('llo1_1', False):
            switch_control |= (1 << 0)  # Bit 0
        if switch_states.get('llo1_2', False):
            switch_control |= (1 << 1)  # Bit 1
        if switch_states.get('llo2_1', False):
            switch_control |= (1 << 2)  # Bit 2
        if switch_states.get('llo2_2', False):
            switch_control |= (1 << 3)  # Bit 3
        if switch_states.get('hlo1_1', False):
            switch_control |= (1 << 4)  # Bit 4
        if switch_states.get('hlo1_2', False):
            switch_control |= (1 << 5)  # Bit 5
        if switch_states.get('hlo1_3', False):
            switch_control |= (1 << 6)  # Bit 6
        if switch_states.get('hlo2_1', False):
            switch_control |= (1 << 7)  # Bit 7
        if switch_states.get('hlo2_2', False):
            switch_control |= (1 << 8)  # Bit 8
        if switch_states.get('hlo2_3', False):
            switch_control |= (1 << 9)  # Bit 9
            
        # Build command frame (little-endian)
        cmd = self.CMD_SWITCH_CONTROL + [
            switch_control & 0xFF,           # BYTE2: low byte (little-endian)
            (switch_control >> 8) & 0xFF   # BYTE3: high byte
        ]
        
        # Log operation
        if self.status_log_callback:
            status_msg = "开关控制调试指令:\n"
            status_msg += f"  低段一本振1通道1: {'开' if switch_states.get('llo1_1', False) else '关'}\n"
            status_msg += f"  低段一本振1通道2: {'开' if switch_states.get('llo1_2', False) else '关'}\n"
            status_msg += f"  低段二本振通道1: {'开' if switch_states.get('llo2_1', False) else '关'}\n"
            status_msg += f"  低段二本振通道2: {'开' if switch_states.get('llo2_2', False) else '关'}\n"
            status_msg += f"  高段一本振1通道1: {'开' if switch_states.get('hlo1_1', False) else '关'}\n"
            status_msg += f"  高段一本振1通道2: {'开' if switch_states.get('hlo1_2', False) else '关'}\n"
            status_msg += f"  高段一本振1通道3: {'开' if switch_states.get('hlo1_3', False) else '关'}\n"
            status_msg += f"  高段二本振1通道1: {'开' if switch_states.get('hlo2_1', False) else '关'}\n"
            status_msg += f"  高段二本振1通道2: {'开' if switch_states.get('hlo2_2', False) else '关'}\n"
            status_msg += f"  高段二本振1通道3: {'开' if switch_states.get('hlo2_3', False) else '关'}"
            self.status_log_callback(status_msg)
            
        # Log raw data
        hex_str = ' '.join(f'{b:02X}' for b in cmd)
        self._log(f"TX: {hex_str} (Switch control: 0x{switch_control:04X})")
        
        # Send command
        if not self.can.send_can_message(cmd):
            self._log("Failed to send switch control command")
            return False
            
        return True
    
    def set_channel_params_freq_synth(self, channel_id, low_freq_mhz=None, high1_freq_mhz=None, 
                                     high2_freq_mhz=None, work_mode=None, switch_control=None):
        """Set channel parameters using 频综 protocol format (20 bytes)
        TEST VERSION - for debugging with 频综 protocol
        
        Args:
            channel_id: Channel ID (0x00-0x09)
            low_freq_mhz: Low oscillator 1 frequency in MHz (4000-6970)
            high1_freq_mhz: High oscillator 1 frequency in MHz (10200-20000)
            high2_freq_mhz: High oscillator 2 frequency in MHz (4970-7480)
            work_mode: Work mode (0=switch, 1=divider, 2=low power)
            switch_control: 16-bit switch control value (optional)
            
        Returns:
            bool: True if successful
        """
        # Default values
        if work_mode is None:
            work_mode = 0x01  # 功分工作模式
        if switch_control is None:
            switch_control = 0x03FF  # All switches ON by default
            
        # Convert frequencies to kHz
        low_freq_khz = int(low_freq_mhz * 1000) if low_freq_mhz else 5000000  # Default 5GHz
        high1_freq_khz = int(high1_freq_mhz * 1000) if high1_freq_mhz else 15000000  # Default 15GHz
        high2_freq_khz = int(high2_freq_mhz * 1000) if high2_freq_mhz else 5970000  # Default ~6GHz (middle of range)
        
        # Build 20-byte message according to 频综 protocol
        # This will be sent as 3 frames: 8 + 8 + 4 bytes
        data = []
        
        # BYTE0: Frame header
        data.append(0xF8)
        
        # BYTE1: Write command
        data.append(0x00)
        
        # BYTE2-3: Switch control (little-endian)
        data.append(switch_control & 0xFF)          # Low byte first
        data.append((switch_control >> 8) & 0xFF)   # High byte
        
        # BYTE4: Channel ID
        data.append(channel_id)
        
        # BYTE5: Special control for LLO2
        llo2_control = 0x00
        if switch_control & 0x0004:  # LLO2_1
            llo2_control |= 0x01
        if switch_control & 0x0008:  # LLO2_2
            llo2_control |= 0x02
        data.append(llo2_control)
        
        # BYTE6: Work mode
        data.append(work_mode & 0xFF)
        
        # BYTE7-10: Low oscillator frequency (little-endian)
        data.append(low_freq_khz & 0xFF)
        data.append((low_freq_khz >> 8) & 0xFF)
        data.append((low_freq_khz >> 16) & 0xFF)
        data.append((low_freq_khz >> 24) & 0xFF)
        
        # BYTE11-14: High oscillator 1 frequency (little-endian)
        data.append(high1_freq_khz & 0xFF)
        data.append((high1_freq_khz >> 8) & 0xFF)
        data.append((high1_freq_khz >> 16) & 0xFF)
        data.append((high1_freq_khz >> 24) & 0xFF)
        
        # BYTE15-18: High oscillator 2 frequency (little-endian)
        data.append(high2_freq_khz & 0xFF)
        data.append((high2_freq_khz >> 8) & 0xFF)
        data.append((high2_freq_khz >> 16) & 0xFF)
        data.append((high2_freq_khz >> 24) & 0xFF)
        
        # BYTE19: Attenuation (0x00 for internal calibration)
        data.append(0x00)
        
        # Log the operation
        if self.status_log_callback:
            self.status_log_callback(
                f"设置参数（频综协议）- 通道{channel_id:02X}\n"
                f"  低段频率: {low_freq_mhz:.3f} MHz\n"
                f"  高段一频率: {high1_freq_mhz:.3f} MHz\n"
                f"  高段二频率: {high2_freq_mhz:.3f} MHz\n"
                f"  工作模式: {work_mode}\n"
                f"  开关控制: 0x{switch_control:04X}"
            )
        
        # Send as CAN frames
        # Frame 1: First 8 bytes
        frame1 = [0xF0, 0x04] + data[0:6]  # F0 04 + first 6 bytes of data
        hex_str1 = ' '.join(f'{b:02X}' for b in frame1)
        self._log(f"TX Frame 1: {hex_str1}")
        
        if not self.can.send_can_message(frame1):
            self._log("Failed to send frame 1")
            return False
            
        time.sleep(0.008)
        
        # Frame 2: Next 8 bytes
        frame2 = data[6:14]  # Next 8 bytes
        hex_str2 = ' '.join(f'{b:02X}' for b in frame2)
        self._log(f"TX Frame 2: {hex_str2}")
        
        if not self.can.send_can_message(frame2):
            self._log("Failed to send frame 2")
            return False
            
        time.sleep(0.008)
        
        # Frame 3: Last 6 bytes
        frame3 = data[14:20]  # Last 6 bytes
        hex_str3 = ' '.join(f'{b:02X}' for b in frame3)
        self._log(f"TX Frame 3: {hex_str3}")
        
        if not self.can.send_can_message(frame3):
            self._log("Failed to send frame 3")
            return False
            
        return True