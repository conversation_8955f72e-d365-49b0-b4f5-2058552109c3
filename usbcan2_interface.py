"""
USBCAN-II Interface Wrapper
Provides a high-level interface for ZLG USBCAN-II device
"""

import time
from ctypes import c_int
from zlgcan import ZCAN, ZCAN_USBCAN2, ZCAN_CHANNEL_INIT_CONFIG, ZCAN_Transmit_Data, ZCAN_STATUS_OK


class USBCAN2Interface:
    """USBCAN-II interface wrapper class"""
    
    def __init__(self):
        self.zcan = ZCAN()
        self.device_handle = None
        self.channel_handle = None
        self.device_type = 3  # ZCAN_USBCAN1 = 3 (works for this USBCAN-II device)
        self.device_index = 0
        
    def open_device(self, channel=0):
        """Open USBCAN-II device and initialize channel
        
        Args:
            channel: CAN channel (0 or 1)
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Open device
            self.device_handle = self.zcan.OpenDevice(self.device_type, self.device_index, 0)
            if self.device_handle == 0:
                print("Failed to open USBCAN-II device")
                return False
                
            
            # Initialize channel
            cfg = ZCAN_CHANNEL_INIT_CONFIG()
            cfg.can_type = 0  # TYPE_CAN
            cfg.config.can.filter = 1  # Enable filter
            cfg.config.can.mode = 0  # Normal mode
            cfg.config.can.acc_code = 0
            cfg.config.can.acc_mask = 0xffffffff
            
            # Set baudrate for USBCAN-II using timing0/timing1
            # Common baudrate settings:
            # 1000kbps: timing0=0x00, timing1=0x14
            # 500kbps:  timing0=0x00, timing1=0x1C
            # 250kbps:  timing0=0x01, timing1=0x1C
            # 125kbps:  timing0=0x03, timing1=0x1C
            # 100kbps:  timing0=0x04, timing1=0x1C
            # For this application, use 500kbps as specified
            cfg.config.can.timing0 = 0x00
            cfg.config.can.timing1 = 0x1C
            
            self.channel_handle = self.zcan.InitCAN(self.device_handle, channel, cfg)
            if self.channel_handle == 0:
                print(f"Failed to initialize CAN channel {channel}")
                return False
                
            
            # Start CAN channel
            ret = self.zcan.StartCAN(self.channel_handle)
            if ret != 1:
                print("Failed to start CAN channel")
                return False
                
            return True
            
        except Exception as e:
            print(f"Error opening device: {e}")
            return False
            
    def close_device(self):
        """Close USBCAN-II device"""
        if self.channel_handle:
            self.zcan.ResetCAN(self.channel_handle)
            self.channel_handle = None
            
        if self.device_handle:
            self.zcan.CloseDevice(self.device_handle)
            self.device_handle = None
            
        
    def send_can_message(self, data):
        """Send single CAN message with ID 0x100
        
        Args:
            data: List of bytes to send (max 8 bytes)
            
        Returns:
            bool: True if successful
        """
        if not self.channel_handle:
            print("Channel not initialized")
            return False
            
        if len(data) > 8:
            print("Data too long for single CAN frame")
            return False
            
        try:
            # Create a single-element array of transmit data
            transmit_data = (ZCAN_Transmit_Data * 1)()
            
            # Fill the structure
            transmit_data[0].transmit_type = 0  # Normal transmit
            transmit_data[0].frame.can_id = 0x100  # Fixed CAN ID from CVI code
            transmit_data[0].frame.can_dlc = len(data)
            transmit_data[0].frame.eff = 0  # Standard frame (11-bit ID)
            transmit_data[0].frame.rtr = 0  # Data frame
            transmit_data[0].frame.err = 0
            transmit_data[0].frame.pad = 0
            
            # Copy data bytes
            for i, byte in enumerate(data):
                transmit_data[0].frame.data[i] = byte
                
            # Send frame
            ret = self.zcan.Transmit(self.channel_handle, transmit_data, 1)
            if ret != 1:
                print(f"Failed to send CAN message, return code: {ret}")
                # Try to get more error info
                try:
                    err_info = self.zcan.GetIProperty(self.device_handle)
                    print(f"Device error info: {err_info}")
                except:
                    pass
                return False
                
            return True
            
        except Exception as e:
            print(f"Error sending CAN message: {e}")
            return False
            
    def send_multi_frame(self, data):
        """Send data across multiple CAN frames if needed
        
        Args:
            data: List of bytes to send
            
        Returns:
            bool: True if all frames sent successfully
        """
        if len(data) <= 8:
            # Single frame
            return self.send_can_message(data)
            
        # Multiple frames needed - send one by one with delay
        success = True
        for i in range(0, len(data), 8):
            chunk = data[i:i+8]
            if not self.send_can_message(chunk):
                success = False
                break
            # Small delay between frames (10ms as per CVI implementation)
            time.sleep(0.01)
            
        return success
        
    def receive_can_message(self, timeout=1000):
        """Receive single CAN message
        
        Args:
            timeout: Timeout in milliseconds
            
        Returns:
            tuple: (success, data) where data is list of bytes
        """
        if not self.channel_handle:
            print("Channel not initialized")
            return False, []
            
        try:
            msgs, count = self.zcan.Receive(self.channel_handle, 1, c_int(timeout))
            
            if count == 0:
                return False, []
                
            # Extract data from first message
            frame = msgs[0]
            data = []
            for i in range(frame.frame.can_dlc):
                data.append(frame.frame.data[i])
                
            return True, data
            
        except Exception as e:
            print(f"Error receiving CAN message: {e}")
            return False, []
            
    def receive_multi_frame(self, expected_frames, timeout=5000):
        """Receive multiple CAN frames and combine data
        
        Args:
            expected_frames: Number of frames to receive
            timeout: Total timeout in milliseconds
            
        Returns:
            tuple: (success, data) where data is combined byte list
        """
        if not self.channel_handle:
            print("Channel not initialized")
            return False, []
            
        try:
            # Receive all frames at once
            msgs, count = self.zcan.Receive(self.channel_handle, expected_frames, c_int(timeout))
            
            if count != expected_frames:
                print(f"Expected {expected_frames} frames, received {count}")
                return False, []
                
            # Combine data from all frames
            data = []
            for i in range(count):
                frame = msgs[i]
                for j in range(frame.frame.can_dlc):
                    data.append(frame.frame.data[j])
                    
            return True, data
            
        except Exception as e:
            print(f"Error receiving multi-frame: {e}")
            return False, []
            
    def clear_receive_buffer(self):
        """Clear any pending messages in receive buffer"""
        if not self.channel_handle:
            return
            
        # Read and discard up to 100 messages with short timeout
        self.zcan.Receive(self.channel_handle, 100, c_int(10))